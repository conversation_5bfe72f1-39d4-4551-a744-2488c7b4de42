"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var dist_exports = {};
__export(dist_exports, {
  BinaryType: () => import_chunk_X37PZICB.BinaryType,
  allEngineEnvVarsSet: () => import_chunk_PXQVM7NP.allEngineEnvVarsSet,
  deprecatedEnvVarMap: () => import_chunk_PXQVM7NP.deprecatedEnvVarMap,
  download: () => import_chunk_7XG7ABFT.download,
  engineEnvVarMap: () => import_chunk_PXQVM7NP.engineEnvVarMap,
  getBinaryEnvVarPath: () => import_chunk_PXQVM7NP.getBinaryEnvVarPath,
  getBinaryName: () => import_chunk_7XG7ABFT.getBinaryName,
  getCacheDir: () => import_chunk_5T2KHPZI.getCacheDir,
  getProxyAgent: () => import_chunk_IGWBG6OP.getProxyAgent,
  getVersion: () => import_chunk_7XG7ABFT.getVersion,
  maybeCopyToTmp: () => import_chunk_7XG7ABFT.maybeCopyToTmp,
  overwriteFile: () => import_chunk_5T2KHPZI.overwriteFile,
  plusX: () => import_chunk_7XG7ABFT.plusX,
  vercelPkgPathRegex: () => import_chunk_7XG7ABFT.vercelPkgPathRegex
});
module.exports = __toCommonJS(dist_exports);
var import_chunk_7XG7ABFT = require("./chunk-7XG7ABFT.js");
var import_chunk_4LX3XBNY = require("./chunk-4LX3XBNY.js");
var import_chunk_MX3HXAU2 = require("./chunk-MX3HXAU2.js");
var import_chunk_RIFXCDQ6 = require("./chunk-RIFXCDQ6.js");
var import_chunk_WKUV47GM = require("./chunk-WKUV47GM.js");
var import_chunk_VTJS2JJN = require("./chunk-VTJS2JJN.js");
var import_chunk_RGVHWUUH = require("./chunk-RGVHWUUH.js");
var import_chunk_5T2KHPZI = require("./chunk-5T2KHPZI.js");
var import_chunk_PXQVM7NP = require("./chunk-PXQVM7NP.js");
var import_chunk_X37PZICB = require("./chunk-X37PZICB.js");
var import_chunk_CWGQAQ3T = require("./chunk-CWGQAQ3T.js");
var import_chunk_IGWBG6OP = require("./chunk-IGWBG6OP.js");
var import_chunk_AH6QHEOA = require("./chunk-AH6QHEOA.js");
