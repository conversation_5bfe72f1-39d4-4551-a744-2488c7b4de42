"use strict";var F=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var U=Object.prototype.hasOwnProperty;var D=(n,t)=>{for(var e in t)F(n,e,{get:t[e],enumerable:!0})},N=(n,t,e,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let _ of B(t))!U.call(n,_)&&_!==e&&F(n,_,{get:()=>t[_],enumerable:!(o=R(t,_))||o.enumerable});return n};var C=n=>N(F({},"__esModule",{value:!0}),n);var vt={};D(vt,{QueryEngine:()=>Z,__wbg_String_88810dfeb4021902:()=>Dn,__wbg_buffer_344d9b41efe96da7:()=>Cn,__wbg_call_53fc3abd42e24ec8:()=>gt,__wbg_call_669127b9d730c650:()=>Zn,__wbg_crypto_58f13aa23ffcb166:()=>Jn,__wbg_done_bc26bf4ada718266:()=>rt,__wbg_entries_6d727b73ee02b7ce:()=>Tt,__wbg_getRandomValues_504510b5564925af:()=>Wn,__wbg_getTime_ed6ee333b702f8fc:()=>bn,__wbg_get_2aff440840bb6202:()=>ct,__wbg_get_4a9aa5157afeb382:()=>nt,__wbg_get_94990005bd6ca07c:()=>Un,__wbg_getwithrefkey_5e6d9547403deab8:()=>Rn,__wbg_globalThis_17eff828815f7d84:()=>st,__wbg_global_46f939f6541643c5:()=>ft,__wbg_has_cdf8b85f6e903c80:()=>un,__wbg_instanceof_ArrayBuffer_c7cc317e5c29cc0d:()=>ht,__wbg_instanceof_Promise_cfbcc42300367513:()=>dn,__wbg_instanceof_Uint8Array_19e6f142a5e7e1e1:()=>mt,__wbg_isArray_38525be7442aa21e:()=>bt,__wbg_isSafeInteger_c38b0a16d0c7cef7:()=>lt,__wbg_iterator_7ee1a391d310f8e4:()=>yn,__wbg_length_a5587d6cd79ab197:()=>yt,__wbg_length_cace2e0b3ddc0502:()=>pn,__wbg_msCrypto_abcb1295e768d1f2:()=>Kn,__wbg_new0_ad75dd38f92424e2:()=>an,__wbg_new_08236689f0afb357:()=>qn,__wbg_new_1b94180eeb48f2a2:()=>In,__wbg_new_c728d68b8b34487e:()=>kn,__wbg_new_d8a000788389a31e:()=>$n,__wbg_new_feb65b865d980ae2:()=>en,__wbg_newnoargs_ccdcae30fd002262:()=>at,__wbg_newwithbyteoffsetandlength_2dc04d99088b15e3:()=>Ln,__wbg_newwithlength_13b5319ab422dcf6:()=>Xn,__wbg_next_15da6a3df9290720:()=>_t,__wbg_next_1989a20442400aaa:()=>et,__wbg_node_523d7bd03ef69fba:()=>Hn,__wbg_now_4579335d3581594c:()=>ln,__wbg_now_8ed1a4454e40ecd1:()=>gn,__wbg_parse_3f0cb48976ca4123:()=>sn,__wbg_process_5b786e71d465a513:()=>Vn,__wbg_push_fd3233d09cf81821:()=>Bn,__wbg_randomFillSync_a0d98aa11c81fe89:()=>zn,__wbg_require_2784e593a4674877:()=>Gn,__wbg_resolve_a3252b2860f0a09e:()=>It,__wbg_self_3fad056edded10bd:()=>it,__wbg_setTimeout_631fe61f31fa2fad:()=>rn,__wbg_set_0ac78a2bc07da03c:()=>Fn,__wbg_set_3355b9f2d3092e3b:()=>vn,__wbg_set_40f7786a25a9cc7e:()=>dt,__wbg_set_841ac57cff3d672b:()=>Mn,__wbg_set_dcfd613a3420f908:()=>pt,__wbg_set_wasm:()=>L,__wbg_stringify_4039297315a25b00:()=>wt,__wbg_subarray_6ca5cfa7fbb9abbe:()=>Pn,__wbg_then_1bbc9edafd859b06:()=>Ft,__wbg_then_89e1c559530b85cf:()=>qt,__wbg_valueOf_ff4b62641803432a:()=>tt,__wbg_value_0570714ff7d75f35:()=>ot,__wbg_versions_c2ab80650590b6a2:()=>Qn,__wbg_window_a4f46c98a61d4089:()=>ut,__wbindgen_bigint_from_i64:()=>Tn,__wbindgen_bigint_from_u64:()=>Sn,__wbindgen_bigint_get_as_i64:()=>At,__wbindgen_boolean_get:()=>xn,__wbindgen_cb_drop:()=>Ot,__wbindgen_closure_wrapper6793:()=>kt,__wbindgen_debug_string:()=>St,__wbindgen_error_new:()=>tn,__wbindgen_in:()=>An,__wbindgen_is_bigint:()=>mn,__wbindgen_is_function:()=>Yn,__wbindgen_is_object:()=>wn,__wbindgen_is_string:()=>En,__wbindgen_is_undefined:()=>cn,__wbindgen_jsval_eq:()=>jn,__wbindgen_jsval_loose_eq:()=>xt,__wbindgen_memory:()=>Nn,__wbindgen_number_get:()=>hn,__wbindgen_number_new:()=>On,__wbindgen_object_clone_ref:()=>_n,__wbindgen_object_drop_ref:()=>fn,__wbindgen_string_get:()=>nn,__wbindgen_string_new:()=>on,__wbindgen_throw:()=>jt,debug_panic:()=>K,getBuildTimeInfo:()=>G});module.exports=C(vt);var h=()=>{};h.prototype=h;let c;function L(n){c=n}const w=new Array(128).fill(void 0);w.push(void 0,null,!0,!1);function r(n){return w[n]}let a=0,T=null;function A(){return(T===null||T.byteLength===0)&&(T=new Uint8Array(c.memory.buffer)),T}const $=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let S=new $("utf-8");const z=typeof S.encodeInto=="function"?function(n,t){return S.encodeInto(n,t)}:function(n,t){const e=S.encode(n);return t.set(e),{read:n.length,written:e.length}};function g(n,t,e){if(e===void 0){const s=S.encode(n),p=t(s.length,1)>>>0;return A().subarray(p,p+s.length).set(s),a=s.length,p}let o=n.length,_=t(o,1)>>>0;const f=A();let u=0;for(;u<o;u++){const s=n.charCodeAt(u);if(s>127)break;f[_+u]=s}if(u!==o){u!==0&&(n=n.slice(u)),_=e(_,o,o=u+n.length*3,1)>>>0;const s=A().subarray(_+u,_+o),p=z(n,s);u+=p.written,_=e(_,o,u,1)>>>0}return a=u,_}function y(n){return n==null}let j=null;function d(){return(j===null||j.byteLength===0)&&(j=new Int32Array(c.memory.buffer)),j}const P=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let k=new P("utf-8",{ignoreBOM:!0,fatal:!0});k.decode();function x(n,t){return n=n>>>0,k.decode(A().subarray(n,n+t))}let m=w.length;function i(n){m===w.length&&w.push(w.length+1);const t=m;return m=w[t],w[t]=n,t}function W(n){n<132||(w[n]=m,m=n)}function b(n){const t=r(n);return W(n),t}let O=null;function J(){return(O===null||O.byteLength===0)&&(O=new Float64Array(c.memory.buffer)),O}let q=null;function V(){return(q===null||q.byteLength===0)&&(q=new BigInt64Array(c.memory.buffer)),q}function I(n){const t=typeof n;if(t=="number"||t=="boolean"||n==null)return`${n}`;if(t=="string")return`"${n}"`;if(t=="symbol"){const _=n.description;return _==null?"Symbol":`Symbol(${_})`}if(t=="function"){const _=n.name;return typeof _=="string"&&_.length>0?`Function(${_})`:"Function"}if(Array.isArray(n)){const _=n.length;let f="[";_>0&&(f+=I(n[0]));for(let u=1;u<_;u++)f+=", "+I(n[u]);return f+="]",f}const e=/\[object ([^\]]+)\]/.exec(toString.call(n));let o;if(e.length>1)o=e[1];else return toString.call(n);if(o=="Object")try{return"Object("+JSON.stringify(n)+")"}catch{return"Object"}return n instanceof Error?`${n.name}: ${n.message}
${n.stack}`:o}const v=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>{c.__wbindgen_export_2.get(n.dtor)(n.a,n.b)});function Q(n,t,e,o){const _={a:n,b:t,cnt:1,dtor:e},f=(...u)=>{_.cnt++;const s=_.a;_.a=0;try{return o(s,_.b,...u)}finally{--_.cnt===0?(c.__wbindgen_export_2.get(_.dtor)(s,_.b),v.unregister(_)):_.a=s}};return f.original=_,v.register(f,_,_),f}function H(n,t,e){c._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h554ff6a6704ac014(n,t,i(e))}function G(){const n=c.getBuildTimeInfo();return b(n)}function K(n){try{const f=c.__wbindgen_add_to_stack_pointer(-16);var t=y(n)?0:g(n,c.__wbindgen_malloc,c.__wbindgen_realloc),e=a;c.debug_panic(f,t,e);var o=d()[f/4+0],_=d()[f/4+1];if(_)throw b(o)}finally{c.__wbindgen_add_to_stack_pointer(16)}}function l(n,t){try{return n.apply(this,t)}catch(e){c.__wbindgen_exn_store(i(e))}}function X(n,t,e,o){c.wasm_bindgen__convert__closures__invoke2_mut__h3412307291f32ce1(n,t,i(e),i(o))}const Y=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>c.__wbg_queryengine_free(n>>>0));class Z{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,Y.unregister(this),t}free(){const t=this.__destroy_into_raw();c.__wbg_queryengine_free(t)}constructor(t,e,o){try{const s=c.__wbindgen_add_to_stack_pointer(-16);c.queryengine_new(s,i(t),i(e),i(o));var _=d()[s/4+0],f=d()[s/4+1],u=d()[s/4+2];if(u)throw b(f);return this.__wbg_ptr=_>>>0,this}finally{c.__wbindgen_add_to_stack_pointer(16)}}connect(t){const e=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,_=c.queryengine_connect(this.__wbg_ptr,e,o);return b(_)}disconnect(t){const e=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,_=c.queryengine_disconnect(this.__wbg_ptr,e,o);return b(_)}query(t,e,o){const _=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),f=a,u=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),s=a;var p=y(o)?0:g(o,c.__wbindgen_malloc,c.__wbindgen_realloc),E=a;const M=c.queryengine_query(this.__wbg_ptr,_,f,u,s,p,E);return b(M)}startTransaction(t,e){const o=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,f=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_startTransaction(this.__wbg_ptr,o,_,f,u);return b(s)}commitTransaction(t,e){const o=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,f=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_commitTransaction(this.__wbg_ptr,o,_,f,u);return b(s)}rollbackTransaction(t,e){const o=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,f=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_rollbackTransaction(this.__wbg_ptr,o,_,f,u);return b(s)}metrics(t){const e=g(t,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,_=c.queryengine_metrics(this.__wbg_ptr,e,o);return b(_)}}function nn(n,t){const e=r(t),o=typeof e=="string"?e:void 0;var _=y(o)?0:g(o,c.__wbindgen_malloc,c.__wbindgen_realloc),f=a;d()[n/4+1]=f,d()[n/4+0]=_}function tn(n,t){const e=new Error(x(n,t));return i(e)}function en(n,t){try{var e={a:n,b:t},o=(f,u)=>{const s=e.a;e.a=0;try{return X(s,e.b,f,u)}finally{e.a=s}};const _=new Promise(o);return i(_)}finally{e.a=e.b=0}}function rn(n,t){return setTimeout(r(n),t>>>0)}function on(n,t){const e=x(n,t);return i(e)}function _n(n){const t=r(n);return i(t)}function cn(n){return r(n)===void 0}function un(){return l(function(n,t){return Reflect.has(r(n),r(t))},arguments)}function sn(){return l(function(n,t){const e=JSON.parse(x(n,t));return i(e)},arguments)}function fn(n){b(n)}function an(){return i(new Date)}function bn(n){return r(n).getTime()}function gn(n){return r(n).now()}function ln(){return Date.now()}function dn(n){let t;try{t=r(n)instanceof Promise}catch{t=!1}return t}function wn(n){const t=r(n);return typeof t=="object"&&t!==null}function pn(n){return r(n).length}function yn(){return i(Symbol.iterator)}function xn(n){const t=r(n);return typeof t=="boolean"?t?1:0:2}function mn(n){return typeof r(n)=="bigint"}function hn(n,t){const e=r(t),o=typeof e=="number"?e:void 0;J()[n/8+1]=y(o)?0:o,d()[n/4+0]=!y(o)}function Tn(n){return i(n)}function An(n,t){return r(n)in r(t)}function Sn(n){const t=BigInt.asUintN(64,n);return i(t)}function jn(n,t){return r(n)===r(t)}function On(n){return i(n)}function qn(){const n=new Array;return i(n)}function Fn(n,t,e){r(n)[t>>>0]=b(e)}function In(){return i(new Map)}function kn(){const n=new Object;return i(n)}function vn(n,t,e){const o=r(n).set(r(t),r(e));return i(o)}function En(n){return typeof r(n)=="string"}function Mn(n,t,e){r(n)[b(t)]=b(e)}function Rn(n,t){const e=r(n)[r(t)];return i(e)}function Bn(n,t){return r(n).push(r(t))}function Un(){return l(function(n,t){const e=r(n)[b(t)];return i(e)},arguments)}function Dn(n,t){const e=String(r(t)),o=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a;d()[n/4+1]=_,d()[n/4+0]=o}function Nn(){const n=c.memory;return i(n)}function Cn(n){const t=r(n).buffer;return i(t)}function Ln(n,t,e){const o=new Uint8Array(r(n),t>>>0,e>>>0);return i(o)}function $n(n){const t=new Uint8Array(r(n));return i(t)}function zn(){return l(function(n,t){r(n).randomFillSync(b(t))},arguments)}function Pn(n,t,e){const o=r(n).subarray(t>>>0,e>>>0);return i(o)}function Wn(){return l(function(n,t){r(n).getRandomValues(r(t))},arguments)}function Jn(n){const t=r(n).crypto;return i(t)}function Vn(n){const t=r(n).process;return i(t)}function Qn(n){const t=r(n).versions;return i(t)}function Hn(n){const t=r(n).node;return i(t)}function Gn(){return l(function(){const n=module.require;return i(n)},arguments)}function Kn(n){const t=r(n).msCrypto;return i(t)}function Xn(n){const t=new Uint8Array(n>>>0);return i(t)}function Yn(n){return typeof r(n)=="function"}function Zn(){return l(function(n,t){const e=r(n).call(r(t));return i(e)},arguments)}function nt(n,t){const e=r(n)[t>>>0];return i(e)}function tt(n){return r(n).valueOf()}function et(){return l(function(n){const t=r(n).next();return i(t)},arguments)}function rt(n){return r(n).done}function ot(n){const t=r(n).value;return i(t)}function _t(n){const t=r(n).next;return i(t)}function ct(){return l(function(n,t){const e=Reflect.get(r(n),r(t));return i(e)},arguments)}function it(){return l(function(){const n=self.self;return i(n)},arguments)}function ut(){return l(function(){const n=window.window;return i(n)},arguments)}function st(){return l(function(){const n=globalThis.globalThis;return i(n)},arguments)}function ft(){return l(function(){const n=global.global;return i(n)},arguments)}function at(n,t){const e=new h(x(n,t));return i(e)}function bt(n){return Array.isArray(r(n))}function gt(){return l(function(n,t,e){const o=r(n).call(r(t),r(e));return i(o)},arguments)}function lt(n){return Number.isSafeInteger(r(n))}function dt(){return l(function(n,t,e){return Reflect.set(r(n),r(t),r(e))},arguments)}function wt(){return l(function(n){const t=JSON.stringify(r(n));return i(t)},arguments)}function pt(n,t,e){r(n).set(r(t),e>>>0)}function yt(n){return r(n).length}function xt(n,t){return r(n)==r(t)}function mt(n){let t;try{t=r(n)instanceof Uint8Array}catch{t=!1}return t}function ht(n){let t;try{t=r(n)instanceof ArrayBuffer}catch{t=!1}return t}function Tt(n){const t=Object.entries(r(n));return i(t)}function At(n,t){const e=r(t),o=typeof e=="bigint"?e:void 0;V()[n/8+1]=y(o)?BigInt(0):o,d()[n/4+0]=!y(o)}function St(n,t){const e=I(r(t)),o=g(e,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a;d()[n/4+1]=_,d()[n/4+0]=o}function jt(n,t){throw new Error(x(n,t))}function Ot(n){const t=b(n).original;return t.cnt--==1?(t.a=0,!0):!1}function qt(n,t){const e=r(n).then(r(t));return i(e)}function Ft(n,t,e){const o=r(n).then(r(t),r(e));return i(o)}function It(n){const t=Promise.resolve(r(n));return i(t)}function kt(n,t,e){const o=Q(n,t,315,H);return i(o)}0&&(module.exports={QueryEngine,__wbg_String_88810dfeb4021902,__wbg_buffer_344d9b41efe96da7,__wbg_call_53fc3abd42e24ec8,__wbg_call_669127b9d730c650,__wbg_crypto_58f13aa23ffcb166,__wbg_done_bc26bf4ada718266,__wbg_entries_6d727b73ee02b7ce,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_ed6ee333b702f8fc,__wbg_get_2aff440840bb6202,__wbg_get_4a9aa5157afeb382,__wbg_get_94990005bd6ca07c,__wbg_getwithrefkey_5e6d9547403deab8,__wbg_globalThis_17eff828815f7d84,__wbg_global_46f939f6541643c5,__wbg_has_cdf8b85f6e903c80,__wbg_instanceof_ArrayBuffer_c7cc317e5c29cc0d,__wbg_instanceof_Promise_cfbcc42300367513,__wbg_instanceof_Uint8Array_19e6f142a5e7e1e1,__wbg_isArray_38525be7442aa21e,__wbg_isSafeInteger_c38b0a16d0c7cef7,__wbg_iterator_7ee1a391d310f8e4,__wbg_length_a5587d6cd79ab197,__wbg_length_cace2e0b3ddc0502,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_ad75dd38f92424e2,__wbg_new_08236689f0afb357,__wbg_new_1b94180eeb48f2a2,__wbg_new_c728d68b8b34487e,__wbg_new_d8a000788389a31e,__wbg_new_feb65b865d980ae2,__wbg_newnoargs_ccdcae30fd002262,__wbg_newwithbyteoffsetandlength_2dc04d99088b15e3,__wbg_newwithlength_13b5319ab422dcf6,__wbg_next_15da6a3df9290720,__wbg_next_1989a20442400aaa,__wbg_node_523d7bd03ef69fba,__wbg_now_4579335d3581594c,__wbg_now_8ed1a4454e40ecd1,__wbg_parse_3f0cb48976ca4123,__wbg_process_5b786e71d465a513,__wbg_push_fd3233d09cf81821,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_resolve_a3252b2860f0a09e,__wbg_self_3fad056edded10bd,__wbg_setTimeout_631fe61f31fa2fad,__wbg_set_0ac78a2bc07da03c,__wbg_set_3355b9f2d3092e3b,__wbg_set_40f7786a25a9cc7e,__wbg_set_841ac57cff3d672b,__wbg_set_dcfd613a3420f908,__wbg_set_wasm,__wbg_stringify_4039297315a25b00,__wbg_subarray_6ca5cfa7fbb9abbe,__wbg_then_1bbc9edafd859b06,__wbg_then_89e1c559530b85cf,__wbg_valueOf_ff4b62641803432a,__wbg_value_0570714ff7d75f35,__wbg_versions_c2ab80650590b6a2,__wbg_window_a4f46c98a61d4089,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper6793,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
