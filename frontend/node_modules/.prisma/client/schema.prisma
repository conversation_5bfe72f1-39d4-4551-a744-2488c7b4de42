generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// User authentication and profile
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  firstName String?  @map("first_name")
  lastName  String?  @map("last_name")
  phone     String?
  avatar    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Multi-tenant associations
  userTenants UserTenant[]

  @@map("users")
}

// Tenant (business/restaurant)
model Tenant {
  id          String   @id @default(uuid())
  name        String
  slug        String   @unique
  email       String
  phone       String?
  address     String?
  description String?
  logo        String?
  settings    Json?
  status      String   @default("active") // active, inactive, suspended
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userTenants UserTenant[]
  menuItems   MenuItem[]
  orders      Order[]

  @@map("tenants")
}

// User-Tenant association (many-to-many with roles)
model UserTenant {
  id       String  @id @default(uuid())
  userId   String  @map("user_id")
  tenantId String  @map("tenant_id")
  role     String  @default("staff") // owner, admin, manager, staff
  isActive Boolean @default(true) @map("is_active")

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([userId, tenantId])
  @@map("user_tenants")
}

// Menu items
model MenuItem {
  id          String   @id @default(uuid())
  tenantId    String   @map("tenant_id")
  name        String
  description String?
  category    String
  price       Decimal  @db.Decimal(10, 2)
  image       String?
  tags        String[]
  allergens   String[]
  isAvailable Boolean  @default(true) @map("is_available")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]

  @@map("menu_items")
}

// Orders
model Order {
  id            String   @id @default(uuid())
  tenantId      String   @map("tenant_id")
  customerName  String?  @map("customer_name")
  customerPhone String?  @map("customer_phone")
  customerEmail String?  @map("customer_email")
  totalAmount   Decimal  @map("total_amount") @db.Decimal(10, 2)
  status        String   @default("pending") // pending, confirmed, preparing, ready, delivered, cancelled
  orderType     String   @default("dine_in") @map("order_type") // dine_in, takeaway, delivery
  tableNumber   String?  @map("table_number")
  notes         String?
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]

  @@map("orders")
}

// Order items
model OrderItem {
  id         String  @id @default(uuid())
  orderId    String  @map("order_id")
  menuItemId String  @map("menu_item_id")
  quantity   Int
  unitPrice  Decimal @map("unit_price") @db.Decimal(10, 2)
  totalPrice Decimal @map("total_price") @db.Decimal(10, 2)
  notes      String?

  // Relations
  order    Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItem MenuItem @relation(fields: [menuItemId], references: [id], onDelete: Cascade)

  @@map("order_items")
}
