{"version": 3, "sources": ["../src/index.ts", "../src/createBrowserClient.ts", "../src/utils/helpers.ts", "../src/utils/constants.ts", "../src/utils/chunker.ts", "../src/createServerClient.ts"], "sourcesContent": ["export * from './createBrowserClient';\nexport * from './createServerClient';\nexport * from './types';\nexport * from './utils';\n", "import { createClient } from '@supabase/supabase-js';\nimport { mergeDeepRight } from 'ramda';\nimport {\n\tDEFAULT_COOKIE_OPTIONS,\n\tcombineChunks,\n\tcreateChunks,\n\tdeleteChunks,\n\tisBrowser\n} from './utils';\nimport { parse, serialize } from 'cookie';\n\nimport type { SupabaseClient } from '@supabase/supabase-js';\nimport type {\n\tGenericSchema,\n\tSupabaseClientOptions\n} from '@supabase/supabase-js/dist/module/lib/types';\nimport type { CookieMethods, CookieOptionsWithName } from './types';\n\nlet cachedBrowserClient: SupabaseClient<any, string> | undefined;\n\nexport function createBrowserClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tsupabaseUrl: string,\n\tsupabaseKey: string,\n\toptions?: SupabaseClientOptions<SchemaName> & {\n\t\tcookies: CookieMethods;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t\tisSingleton?: boolean;\n\t}\n) {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`\n\t\t);\n\t}\n\n\tlet cookies: CookieMethods = {};\n\tlet isSingleton = true;\n\tlet cookieOptions: CookieOptionsWithName | undefined;\n\tlet userDefinedClientOptions;\n\n\tif (options) {\n\t\t({ cookies, isSingleton = true, cookieOptions, ...userDefinedClientOptions } = options);\n\t}\n\n\tconst cookieClientOptions = {\n\t\tglobal: {\n\t\t\theaders: {\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}/${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tflowType: 'pkce',\n\t\t\tautoRefreshToken: isBrowser(),\n\t\t\tdetectSessionInUrl: isBrowser(),\n\t\t\tpersistSession: true,\n\t\t\tstorage: {\n\t\t\t\t// this client is used on the browser so cookies can be trusted\n\t\t\t\tisServer: false,\n\t\t\t\tgetItem: async (key: string) => {\n\t\t\t\t\tconst chunkedCookie = await combineChunks(key, async (chunkName) => {\n\t\t\t\t\t\tif (typeof cookies.get === 'function') {\n\t\t\t\t\t\t\treturn await cookies.get(chunkName);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (isBrowser()) {\n\t\t\t\t\t\t\tconst cookie = parse(document.cookie);\n\t\t\t\t\t\t\treturn cookie[chunkName];\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn chunkedCookie;\n\t\t\t\t},\n\t\t\t\tsetItem: async (key: string, value: string) => {\n\t\t\t\t\tconst chunks = await createChunks(key, value);\n\t\t\t\t\tawait Promise.all(\n\t\t\t\t\t\tchunks.map(async (chunk) => {\n\t\t\t\t\t\t\tif (typeof cookies.set === 'function') {\n\t\t\t\t\t\t\t\tawait cookies.set(chunk.name, chunk.value, {\n\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\tmaxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif (isBrowser()) {\n\t\t\t\t\t\t\t\t\tdocument.cookie = serialize(chunk.name, chunk.value, {\n\t\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\t\tmaxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\tremoveItem: async (key: string) => {\n\t\t\t\t\tif (typeof cookies.remove === 'function' && typeof cookies.get !== 'function') {\n\t\t\t\t\t\tconsole.log(\n\t\t\t\t\t\t\t'Removing chunked cookie without a `get` method is not supported.\\n\\n\\tWhen you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client'\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tawait deleteChunks(\n\t\t\t\t\t\tkey,\n\t\t\t\t\t\tasync (chunkName) => {\n\t\t\t\t\t\t\tif (typeof cookies.get === 'function') {\n\t\t\t\t\t\t\t\treturn await cookies.get(chunkName);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (isBrowser()) {\n\t\t\t\t\t\t\t\tconst documentCookies = parse(document.cookie);\n\t\t\t\t\t\t\t\treturn documentCookies[chunkName];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tasync (chunkName) => {\n\t\t\t\t\t\t\tif (typeof cookies.remove === 'function') {\n\t\t\t\t\t\t\t\tawait cookies.remove(chunkName, {\n\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\tmaxAge: 0\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif (isBrowser()) {\n\t\t\t\t\t\t\t\t\tdocument.cookie = serialize(chunkName, '', {\n\t\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\t\tmaxAge: 0\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n\n\t// Overwrites default client config with any user defined options\n\tconst clientOptions = mergeDeepRight(\n\t\tcookieClientOptions,\n\t\tuserDefinedClientOptions\n\t) as SupabaseClientOptions<SchemaName>;\n\n\tif (isSingleton) {\n\t\t// The `Singleton` pattern is the default to simplify the instantiation\n\t\t// of a Supabase client in the browser - there must only be one\n\n\t\tconst browser = isBrowser();\n\n\t\tif (browser && cachedBrowserClient) {\n\t\t\treturn cachedBrowserClient as SupabaseClient<Database, SchemaName, Schema>;\n\t\t}\n\n\t\tconst client = createClient<Database, SchemaName, Schema>(\n\t\t\tsupabaseUrl,\n\t\t\tsupabaseKey,\n\t\t\tclientOptions\n\t\t);\n\n\t\tif (browser) {\n\t\t\t// The client should only be cached in the browser\n\t\t\tcachedBrowserClient = client;\n\t\t}\n\n\t\treturn client;\n\t}\n\n\t// This allows for multiple Supabase clients, which may be required when using\n\t// multiple schemas. The user will be responsible for ensuring a single\n\t// instance of Supabase is used for each schema in the browser.\n\treturn createClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, clientOptions);\n}\n", "export { parse, serialize } from 'cookie';\n\nexport function isBrowser() {\n\treturn typeof window !== 'undefined' && typeof window.document !== 'undefined';\n}\n", "import { CookieOptions } from '../types';\n\nexport const DEFAULT_COOKIE_OPTIONS: CookieOptions = {\n\tpath: '/',\n\tsameSite: 'lax',\n\thttpOnly: false,\n\tmaxAge: 60 * 60 * 24 * 365 * 1000\n};\n", "interface Chunk {\n\tname: string;\n\tvalue: string;\n}\n\nconst MAX_CHUNK_SIZE = 3180;\n\n/**\n * create chunks from a string and return an array of object\n */\nexport function createChunks(key: string, value: string, chunkSize?: number): Chunk[] {\n\tconst resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n\n\tlet encodedValue = encodeURIComponent(value);\n\n\tif (encodedValue.length <= resolvedChunkSize) {\n\t\treturn [{ name: key, value }];\n\t}\n\n\tconst chunks: string[] = [];\n\n\twhile (encodedValue.length > 0) {\n\t\tlet encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n\n\t\tconst lastEscapePos = encodedChunkHead.lastIndexOf('%');\n\n\t\t// Check if the last escaped character is truncated.\n\t\tif (lastEscapePos > resolvedChunkSize - 3) {\n\t\t\t// If so, reslice the string to exclude the whole escape sequence.\n\t\t\t// We only reduce the size of the string as the chunk must\n\t\t\t// be smaller than the chunk size.\n\t\t\tencodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n\t\t}\n\n\t\tlet valueHead: string = '';\n\n\t\t// Check if the chunk was split along a valid unicode boundary.\n\t\twhile (encodedChunkHead.length > 0) {\n\t\t\ttry {\n\t\t\t\t// Try to decode the chunk back and see if it is valid.\n\t\t\t\t// Stop when the chunk is valid.\n\t\t\t\tvalueHead = decodeURIComponent(encodedChunkHead);\n\t\t\t\tbreak;\n\t\t\t} catch (error) {\n\t\t\t\tif (\n\t\t\t\t\terror instanceof URIError &&\n\t\t\t\t\tencodedChunkHead.at(-3) === '%' &&\n\t\t\t\t\tencodedChunkHead.length > 3\n\t\t\t\t) {\n\t\t\t\t\tencodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n\t\t\t\t} else {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tchunks.push(valueHead);\n\t\tencodedValue = encodedValue.slice(encodedChunkHead.length);\n\t}\n\n\treturn chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n\n// Get fully constructed chunks\nexport async function combineChunks(\n\tkey: string,\n\tretrieveChunk: (name: string) => Promise<string | null | undefined> | string | null | undefined\n) {\n\tconst value = await retrieveChunk(key);\n\n\tif (value) {\n\t\treturn value;\n\t}\n\n\tlet values: string[] = [];\n\n\tfor (let i = 0; ; i++) {\n\t\tconst chunkName = `${key}.${i}`;\n\t\tconst chunk = await retrieveChunk(chunkName);\n\n\t\tif (!chunk) {\n\t\t\tbreak;\n\t\t}\n\n\t\tvalues.push(chunk);\n\t}\n\n\tif (values.length > 0) {\n\t\treturn values.join('');\n\t}\n}\n\nexport async function deleteChunks(\n\tkey: string,\n\tretrieveChunk: (name: string) => Promise<string | null | undefined> | string | null | undefined,\n\tremoveChunk: (name: string) => Promise<void> | void\n) {\n\tconst value = await retrieveChunk(key);\n\n\tif (value) {\n\t\tawait removeChunk(key);\n\t\treturn;\n\t}\n\n\tfor (let i = 0; ; i++) {\n\t\tconst chunkName = `${key}.${i}`;\n\t\tconst chunk = await retrieveChunk(chunkName);\n\n\t\tif (!chunk) {\n\t\t\tbreak;\n\t\t}\n\n\t\tawait removeChunk(chunkName);\n\t}\n}\n", "import { createClient } from '@supabase/supabase-js';\nimport { mergeDeepRight } from 'ramda';\nimport {\n\tDEFAULT_COOKIE_OPTIONS,\n\tcombineChunks,\n\tcreateChunks,\n\tdeleteChunks,\n\tisBrowser\n} from './utils';\n\nimport type {\n\tGenericSchema,\n\tSupabaseClientOptions\n} from '@supabase/supabase-js/dist/module/lib/types';\nimport type { CookieOptionsWithName, CookieMethods } from './types';\n\nexport function createServerClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tsupabaseUrl: string,\n\tsupabaseKey: string,\n\toptions: SupabaseClientOptions<SchemaName> & {\n\t\tcookies: CookieMethods;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t}\n) {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`\n\t\t);\n\t}\n\n\tconst { cookies, cookieOptions, ...userDefinedClientOptions } = options;\n\n\t// use the cookie name as the storageKey value if it's set\n\tif (cookieOptions?.name) {\n\t\tuserDefinedClientOptions.auth = {\n\t\t\t...userDefinedClientOptions.auth,\n\t\t\tstorageKey: cookieOptions.name\n\t\t};\n\t}\n\n\tconst cookieClientOptions = {\n\t\tglobal: {\n\t\t\theaders: {\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}/${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tflowType: 'pkce',\n\t\t\tautoRefreshToken: isBrowser(),\n\t\t\tdetectSessionInUrl: isBrowser(),\n\t\t\tpersistSession: true,\n\t\t\tstorage: {\n\t\t\t\t// to signal to the libraries that these cookies are coming from a server environment and their value should not be trusted\n\t\t\t\tisServer: true,\n\t\t\t\tgetItem: async (key: string) => {\n\t\t\t\t\tconst chunkedCookie = await combineChunks(key, async (chunkName: string) => {\n\t\t\t\t\t\tif (typeof cookies.get === 'function') {\n\t\t\t\t\t\t\treturn await cookies.get(chunkName);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn chunkedCookie;\n\t\t\t\t},\n\t\t\t\tsetItem: async (key: string, value: string) => {\n\t\t\t\t\tconst chunks = createChunks(key, value);\n\t\t\t\t\tawait Promise.all(\n\t\t\t\t\t\tchunks.map(async (chunk) => {\n\t\t\t\t\t\t\tif (typeof cookies.set === 'function') {\n\t\t\t\t\t\t\t\tawait cookies.set(chunk.name, chunk.value, {\n\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\tmaxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\tremoveItem: async (key: string) => {\n\t\t\t\t\tif (typeof cookies.remove === 'function' && typeof cookies.get !== 'function') {\n\t\t\t\t\t\tconsole.log(\n\t\t\t\t\t\t\t'Removing chunked cookie without a `get` method is not supported.\\n\\n\\tWhen you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client'\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tdeleteChunks(\n\t\t\t\t\t\tkey,\n\t\t\t\t\t\tasync (chunkName) => {\n\t\t\t\t\t\t\tif (typeof cookies.get === 'function') {\n\t\t\t\t\t\t\t\treturn await cookies.get(chunkName);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tasync (chunkName) => {\n\t\t\t\t\t\t\tif (typeof cookies.remove === 'function') {\n\t\t\t\t\t\t\t\treturn await cookies.remove(chunkName, {\n\t\t\t\t\t\t\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t\t\t\t\t\t\t...cookieOptions,\n\t\t\t\t\t\t\t\t\tmaxAge: 0\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n\n\t// Overwrites default client config with any user defined options\n\tconst clientOptions = mergeDeepRight(\n\t\tcookieClientOptions,\n\t\tuserDefinedClientOptions\n\t) as SupabaseClientOptions<SchemaName>;\n\n\treturn createClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, clientOptions);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,yBAA6B;AAC7B,mBAA+B;;;ACD/B,oBAAiC;AAE1B,SAAS,YAAY;AAC3B,SAAO,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AACpE;;;ACFO,IAAM,yBAAwC;AAAA,EACpD,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ,KAAK,KAAK,KAAK,MAAM;AAC9B;;;ACFA,IAAM,iBAAiB;AAKhB,SAAS,aAAa,KAAa,OAAe,WAA6B;AACrF,QAAM,oBAAoB,aAAa;AAEvC,MAAI,eAAe,mBAAmB,KAAK;AAE3C,MAAI,aAAa,UAAU,mBAAmB;AAC7C,WAAO,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;AAAA,EAC7B;AAEA,QAAM,SAAmB,CAAC;AAE1B,SAAO,aAAa,SAAS,GAAG;AAC/B,QAAI,mBAAmB,aAAa,MAAM,GAAG,iBAAiB;AAE9D,UAAM,gBAAgB,iBAAiB,YAAY,GAAG;AAGtD,QAAI,gBAAgB,oBAAoB,GAAG;AAI1C,yBAAmB,iBAAiB,MAAM,GAAG,aAAa;AAAA,IAC3D;AAEA,QAAI,YAAoB;AAGxB,WAAO,iBAAiB,SAAS,GAAG;AACnC,UAAI;AAGH,oBAAY,mBAAmB,gBAAgB;AAC/C;AAAA,MACD,SAAS,OAAP;AACD,YACC,iBAAiB,YACjB,iBAAiB,GAAG,EAAE,MAAM,OAC5B,iBAAiB,SAAS,GACzB;AACD,6BAAmB,iBAAiB,MAAM,GAAG,iBAAiB,SAAS,CAAC;AAAA,QACzE,OAAO;AACN,gBAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAEA,WAAO,KAAK,SAAS;AACrB,mBAAe,aAAa,MAAM,iBAAiB,MAAM;AAAA,EAC1D;AAEA,SAAO,OAAO,IAAI,CAACA,QAAO,OAAO,EAAE,MAAM,GAAG,OAAO,KAAK,OAAAA,OAAM,EAAE;AACjE;AAGA,eAAsB,cACrB,KACA,eACC;AACD,QAAM,QAAQ,MAAM,cAAc,GAAG;AAErC,MAAI,OAAO;AACV,WAAO;AAAA,EACR;AAEA,MAAI,SAAmB,CAAC;AAExB,WAAS,IAAI,KAAK,KAAK;AACtB,UAAM,YAAY,GAAG,OAAO;AAC5B,UAAM,QAAQ,MAAM,cAAc,SAAS;AAE3C,QAAI,CAAC,OAAO;AACX;AAAA,IACD;AAEA,WAAO,KAAK,KAAK;AAAA,EAClB;AAEA,MAAI,OAAO,SAAS,GAAG;AACtB,WAAO,OAAO,KAAK,EAAE;AAAA,EACtB;AACD;AAEA,eAAsB,aACrB,KACA,eACA,aACC;AACD,QAAM,QAAQ,MAAM,cAAc,GAAG;AAErC,MAAI,OAAO;AACV,UAAM,YAAY,GAAG;AACrB;AAAA,EACD;AAEA,WAAS,IAAI,KAAK,KAAK;AACtB,UAAM,YAAY,GAAG,OAAO;AAC5B,UAAM,QAAQ,MAAM,cAAc,SAAS;AAE3C,QAAI,CAAC,OAAO;AACX;AAAA,IACD;AAEA,UAAM,YAAY,SAAS;AAAA,EAC5B;AACD;;;AHzGA,IAAAC,iBAAiC;AASjC,IAAI;AAEG,SAAS,oBASf,aACA,aACA,SAKC;AACD,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,IACD;AAAA,EACD;AAEA,MAAI,UAAyB,CAAC;AAC9B,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AAEJ,MAAI,SAAS;AACZ,KAAC,EAAE,SAAS,cAAc,MAAM,eAAe,GAAG,yBAAyB,IAAI;AAAA,EAChF;AAEA,QAAM,sBAAsB;AAAA,IAC3B,QAAQ;AAAA,MACP,SAAS;AAAA,QACR,iBAAiB,GAAG,kBAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,UAAU;AAAA,MACV,kBAAkB,UAAU;AAAA,MAC5B,oBAAoB,UAAU;AAAA,MAC9B,gBAAgB;AAAA,MAChB,SAAS;AAAA;AAAA,QAER,UAAU;AAAA,QACV,SAAS,OAAO,QAAgB;AAC/B,gBAAM,gBAAgB,MAAM,cAAc,KAAK,OAAO,cAAc;AACnE,gBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,qBAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,YACnC;AACA,gBAAI,UAAU,GAAG;AAChB,oBAAM,aAAS,sBAAM,SAAS,MAAM;AACpC,qBAAO,OAAO,SAAS;AAAA,YACxB;AAAA,UACD,CAAC;AACD,iBAAO;AAAA,QACR;AAAA,QACA,SAAS,OAAO,KAAa,UAAkB;AAC9C,gBAAM,SAAS,MAAM,aAAa,KAAK,KAAK;AAC5C,gBAAM,QAAQ;AAAA,YACb,OAAO,IAAI,OAAO,UAAU;AAC3B,kBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,sBAAM,QAAQ,IAAI,MAAM,MAAM,MAAM,OAAO;AAAA,kBAC1C,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,QAAQ,uBAAuB;AAAA,gBAChC,CAAC;AAAA,cACF,OAAO;AACN,oBAAI,UAAU,GAAG;AAChB,2BAAS,aAAS,0BAAU,MAAM,MAAM,MAAM,OAAO;AAAA,oBACpD,GAAG;AAAA,oBACH,GAAG;AAAA,oBACH,QAAQ,uBAAuB;AAAA,kBAChC,CAAC;AAAA,gBACF;AAAA,cACD;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,QACA,YAAY,OAAO,QAAgB;AAClC,cAAI,OAAO,QAAQ,WAAW,cAAc,OAAO,QAAQ,QAAQ,YAAY;AAC9E,oBAAQ;AAAA,cACP;AAAA,YACD;AACA;AAAA,UACD;AAEA,gBAAM;AAAA,YACL;AAAA,YACA,OAAO,cAAc;AACpB,kBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,uBAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,cACnC;AACA,kBAAI,UAAU,GAAG;AAChB,sBAAM,sBAAkB,sBAAM,SAAS,MAAM;AAC7C,uBAAO,gBAAgB,SAAS;AAAA,cACjC;AAAA,YACD;AAAA,YACA,OAAO,cAAc;AACpB,kBAAI,OAAO,QAAQ,WAAW,YAAY;AACzC,sBAAM,QAAQ,OAAO,WAAW;AAAA,kBAC/B,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,QAAQ;AAAA,gBACT,CAAC;AAAA,cACF,OAAO;AACN,oBAAI,UAAU,GAAG;AAChB,2BAAS,aAAS,0BAAU,WAAW,IAAI;AAAA,oBAC1C,GAAG;AAAA,oBACH,GAAG;AAAA,oBACH,QAAQ;AAAA,kBACT,CAAC;AAAA,gBACF;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAGA,QAAM,oBAAgB;AAAA,IACrB;AAAA,IACA;AAAA,EACD;AAEA,MAAI,aAAa;AAIhB,UAAM,UAAU,UAAU;AAE1B,QAAI,WAAW,qBAAqB;AACnC,aAAO;AAAA,IACR;AAEA,UAAM,aAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,SAAS;AAEZ,4BAAsB;AAAA,IACvB;AAEA,WAAO;AAAA,EACR;AAKA,aAAO,iCAA2C,aAAa,aAAa,aAAa;AAC1F;;;AIhLA,IAAAC,sBAA6B;AAC7B,IAAAC,gBAA+B;AAexB,SAAS,mBASf,aACA,aACA,SAIC;AACD,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,IACD;AAAA,EACD;AAEA,QAAM,EAAE,SAAS,eAAe,GAAG,yBAAyB,IAAI;AAGhE,MAAI,+CAAe,MAAM;AACxB,6BAAyB,OAAO;AAAA,MAC/B,GAAG,yBAAyB;AAAA,MAC5B,YAAY,cAAc;AAAA,IAC3B;AAAA,EACD;AAEA,QAAM,sBAAsB;AAAA,IAC3B,QAAQ;AAAA,MACP,SAAS;AAAA,QACR,iBAAiB,GAAG,kBAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,UAAU;AAAA,MACV,kBAAkB,UAAU;AAAA,MAC5B,oBAAoB,UAAU;AAAA,MAC9B,gBAAgB;AAAA,MAChB,SAAS;AAAA;AAAA,QAER,UAAU;AAAA,QACV,SAAS,OAAO,QAAgB;AAC/B,gBAAM,gBAAgB,MAAM,cAAc,KAAK,OAAO,cAAsB;AAC3E,gBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,qBAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,YACnC;AAAA,UACD,CAAC;AACD,iBAAO;AAAA,QACR;AAAA,QACA,SAAS,OAAO,KAAa,UAAkB;AAC9C,gBAAM,SAAS,aAAa,KAAK,KAAK;AACtC,gBAAM,QAAQ;AAAA,YACb,OAAO,IAAI,OAAO,UAAU;AAC3B,kBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,sBAAM,QAAQ,IAAI,MAAM,MAAM,MAAM,OAAO;AAAA,kBAC1C,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,QAAQ,uBAAuB;AAAA,gBAChC,CAAC;AAAA,cACF;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,QACA,YAAY,OAAO,QAAgB;AAClC,cAAI,OAAO,QAAQ,WAAW,cAAc,OAAO,QAAQ,QAAQ,YAAY;AAC9E,oBAAQ;AAAA,cACP;AAAA,YACD;AACA;AAAA,UACD;AAEA;AAAA,YACC;AAAA,YACA,OAAO,cAAc;AACpB,kBAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,uBAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,cACnC;AAAA,YACD;AAAA,YACA,OAAO,cAAc;AACpB,kBAAI,OAAO,QAAQ,WAAW,YAAY;AACzC,uBAAO,MAAM,QAAQ,OAAO,WAAW;AAAA,kBACtC,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,QAAQ;AAAA,gBACT,CAAC;AAAA,cACF;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAGA,QAAM,oBAAgB;AAAA,IACrB;AAAA,IACA;AAAA,EACD;AAEA,aAAO,kCAA2C,aAAa,aAAa,aAAa;AAC1F;", "names": ["value", "import_cookie", "import_supabase_js", "import_ramda"]}