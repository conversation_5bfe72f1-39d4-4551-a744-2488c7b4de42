{"name": "@prisma/config", "version": "6.12.0", "description": "Internal package used to define and read Prisma configuration files", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/config"}, "license": "Apache-2.0", "author": "<PERSON> <<EMAIL>>", "dependencies": {"jiti": "2.4.2"}, "devDependencies": {"@swc/core": "1.11.5", "@swc/jest": "0.2.37", "cross-env": "7.0.3", "effect": "3.16.12", "jest": "29.7.0", "jest-junit": "16.0.0", "@prisma/driver-adapter-utils": "6.12.0", "@prisma/get-platform": "6.12.0"}, "files": ["dist"], "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "cross-env NODE_OPTIONS='--experimental-vm-modules' jest"}}