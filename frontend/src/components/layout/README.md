# TapDine Layout System

A comprehensive, scalable layout system for the TapDine B2B multi-tenant SaaS platform built with Next.js and Tailwind CSS.

## 🏗️ Architecture Overview

The layout system provides a complete dashboard experience with:

- **Responsive Design**: Mobile-first approach with collapsible sidebar
- **Multi-tenant Support**: Tenant-aware navigation and branding
- **Accessibility**: WCAG compliant with proper ARIA roles
- **Dark Mode**: System-wide dark/light theme toggle
- **Modular Components**: Reusable, clean component architecture

## 📁 Component Structure

```
components/layout/
├── DashboardLayout.tsx     # Main layout wrapper
├── TopNavigation.tsx       # Top navigation bar
├── Sidebar.tsx            # Left sidebar navigation
├── MobileOverlay.tsx      # Mobile sidebar overlay
├── PageHeader.tsx         # Reusable page headers
├── LoadingSpinner.tsx     # Loading states
└── README.md              # This file
```

## 🚀 Usage Examples

### Basic Layout Implementation

```tsx
// app/[tenant]/layout.tsx
import { DashboardLayout } from '@/components/layout/DashboardLayout';

export default function TenantLayout({ children }) {
  return (
    <TenantProvider>
      <ProtectedRoute>
        <DashboardLayout>
          {children}
        </DashboardLayout>
      </ProtectedRoute>
    </TenantProvider>
  );
}
```

### Page with Header

```tsx
// app/[tenant]/dashboard/page.tsx
import { PageHeader } from '@/components/layout/PageHeader';

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        subtitle="Welcome to your restaurant dashboard"
        actions={
          <button className="btn-primary">
            New Order
          </button>
        }
      />
      <div>{/* Page content */}</div>
    </div>
  );
}
```

### Page with Breadcrumbs

```tsx
<PageHeader
  title="Menu Management"
  subtitle="Manage your restaurant's menu items"
  breadcrumbs={[
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Menu' },
  ]}
  actions={<button>Add Item</button>}
/>
```

## 🎨 Design System

### Color Scheme
- **Primary**: Custom primary colors (primary-600, primary-700) - #064 based color scheme
- **Gray Scale**: gray-50 to gray-900
- **Success**: green-600
- **Warning**: yellow-600
- **Error**: red-600

### Typography
- **Headings**: font-bold, various sizes
- **Body**: font-medium, font-normal
- **Mono**: font-mono for code

### Spacing
- **Consistent**: 4px base unit (space-1 = 4px)
- **Page Padding**: p-4 lg:p-6
- **Component Spacing**: space-y-6, space-x-4

## 📱 Responsive Behavior

### Desktop (lg: 1024px+)
- Fixed sidebar (w-64)
- Full navigation visible
- Spacious layout

### Tablet (md: 768px - lg: 1023px)
- Collapsible sidebar
- Compact navigation
- Optimized spacing

### Mobile (< md: 768px)
- Hidden sidebar by default
- Hamburger menu trigger
- Overlay navigation
- Touch-friendly targets

## ♿ Accessibility Features

### ARIA Compliance
- `role="banner"` on header
- `role="navigation"` on sidebar
- `role="main"` on content area
- `aria-label` on interactive elements
- `aria-expanded` on dropdowns

### Keyboard Navigation
- Tab-accessible navigation
- Focus indicators
- Escape key support
- Skip links available

### Screen Reader Support
- Semantic HTML structure
- Proper heading hierarchy
- Alternative text for icons
- Status announcements

## 🌙 Dark Mode Implementation

### Theme Toggle
```tsx
const [darkMode, setDarkMode] = useState(false);

const toggleDarkMode = () => {
  const newDarkMode = !darkMode;
  setDarkMode(newDarkMode);
  localStorage.setItem('darkMode', newDarkMode.toString());
  
  if (newDarkMode) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};
```

### Dark Mode Classes
- Background: `bg-gray-50 dark:bg-gray-900`
- Text: `text-gray-900 dark:text-white`
- Borders: `border-gray-200 dark:border-gray-700`
- Hover: `hover:bg-gray-100 dark:hover:bg-gray-800`

## 🔧 Customization

### Adding Navigation Items

```tsx
// In Sidebar.tsx
const navigationItems = [
  {
    name: 'New Page',
    href: '/new-page',
    icon: <YourIcon className="w-5 h-5" />,
  },
  // ... other items
];
```

### Custom Theme Colors

```tsx
// In tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
      },
    },
  },
};
```

### Tenant Branding

```tsx
// Custom tenant colors
const tenantColors = {
  primary: tenant?.primary_color || '#3b82f6',
  secondary: tenant?.secondary_color || '#f3f4f6',
};
```

## 🚀 Performance Optimizations

### Code Splitting
- Dynamic imports for heavy components
- Lazy loading for non-critical UI
- Route-based code splitting

### Caching
- Static generation for tenant routes
- Server-side rendering for dynamic content
- Browser caching for static assets

### Bundle Size
- Tree-shaking unused code
- Optimized icon imports
- Compressed assets

## 🔒 Security Considerations

### Route Protection
- All tenant routes require authentication
- Role-based access control
- Tenant isolation enforcement

### Data Sanitization
- XSS prevention in dynamic content
- Safe HTML rendering
- Input validation

## 📊 Analytics & Monitoring

### User Interactions
- Navigation tracking
- Feature usage analytics
- Performance monitoring

### Error Handling
- Graceful fallbacks
- Error boundary components
- User feedback systems

## 🛠️ Development Guidelines

### Component Creation
1. Use TypeScript for type safety
2. Include proper ARIA attributes
3. Support dark mode variants
4. Add responsive breakpoints
5. Include loading states

### Testing
- Unit tests for components
- Integration tests for layouts
- Accessibility testing
- Cross-browser compatibility

### Documentation
- Inline code comments
- Component prop documentation
- Usage examples
- Change log maintenance

## 🔄 Migration Guide

### From Previous Layout
1. Update imports to new component paths
2. Replace old navigation with new Sidebar
3. Update page headers to use PageHeader
4. Add dark mode support
5. Update responsive breakpoints

### Breaking Changes
- Navigation structure updated
- CSS classes reorganized
- Props interface changes
- Theme system updated

## 📚 Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Next.js App Router](https://nextjs.org/docs/app)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [Web Content Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

## 🐛 Troubleshooting

### Common Issues

1. **Sidebar not showing**: Check TenantProvider context
2. **Dark mode not working**: Verify localStorage access
3. **Mobile menu issues**: Check overlay z-index
4. **Navigation active states**: Verify route matching logic

### Debug Mode
```tsx
// Enable debug logging
const DEBUG = process.env.NODE_ENV === 'development';
if (DEBUG) console.log('Layout state:', { isOpen, darkMode });
```

## 🤝 Contributing

1. Follow existing code patterns
2. Test on multiple devices
3. Update documentation
4. Add accessibility features
5. Consider performance impact