/**
 * Protected route component with tenant isolation
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import { User } from '@/types/auth.types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

const roleHierarchy = {
  staff: 1,
  manager: 2,
  admin: 3,
  owner: 4,
};

export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermissions,
  fallback,
}: ProtectedRouteProps) {
  const { user, tenant, isLoading, error } = useTenant();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      // Redirect to login if not authenticated
      const currentPath = window.location.pathname;
      const loginUrl = `/login?returnUrl=${encodeURIComponent(currentPath)}`;
      router.push(loginUrl);
    }
  }, [user, isLoading, router]);

  // Show loading state
  if (isLoading) {
    return fallback || <div>Loading...</div>;
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-xl font-semibold text-red-600">Error</h2>
        <p className="text-gray-600 mt-2">{error.message}</p>
        <button
          onClick={() => router.push('/login')}
          className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
        >
          Go to Login
        </button>
      </div>
    );
  }

  // Check if user is authenticated
  if (!user || !tenant) {
    return null; // Will redirect in useEffect
  }

  // Check tenant status
  if (tenant.status !== 'active') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-xl font-semibold text-orange-600">Tenant Suspended</h2>
        <p className="text-gray-600 mt-2">
          This organization&apos;s account is currently suspended.
        </p>
        <button
          onClick={() => router.push('/login')}
          className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
        >
          Switch Organization
        </button>
      </div>
    );
  }

  // Check role requirements
  if (requiredRole && !hasRequiredRole(user, requiredRole)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
        <p className="text-gray-600 mt-2">
          You don&apos;t have the required permissions to access this page.
        </p>
        <button
          onClick={() => router.push('/dashboard')}
          className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
        >
          Go to Dashboard
        </button>
      </div>
    );
  }

  // Check permission requirements
  if (requiredPermissions && !hasRequiredPermissions(user, requiredPermissions)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
        <p className="text-gray-600 mt-2">
          You don&apos;t have the required permissions to access this page.
        </p>
        <button
          onClick={() => router.push('/dashboard')}
          className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
        >
          Go to Dashboard
        </button>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Check if user has required role
 */
function hasRequiredRole(user: User, requiredRole: string): boolean {
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  return userLevel >= requiredLevel;
}

/**
 * Check if user has required permissions
 */
function hasRequiredPermissions(user: User, requiredPermissions: string[]): boolean {
  // In a real app, you would check user permissions from the database
  // For now, we'll use role-based permissions
  const rolePermissions = {
    owner: ['*'], // Owner has all permissions
    admin: ['read', 'write', 'delete', 'manage_users', 'manage_settings'],
    manager: ['read', 'write', 'delete'],
    staff: ['read', 'write'],
  };

  const userPermissions = rolePermissions[user.role as keyof typeof rolePermissions] || [];
  
  // Check if user has all required permissions
  return requiredPermissions.every(permission => 
    userPermissions.includes('*') || userPermissions.includes(permission)
  );
}

/**
 * Higher-order component for protecting routes
 */
export function withAuth<T extends {}>(
  Component: React.ComponentType<T>,
  options?: {
    requiredRole?: string;
    requiredPermissions?: string[];
    fallback?: React.ReactNode;
  }
) {
  return function AuthenticatedComponent(props: T) {
    return (
      <ProtectedRoute
        requiredRole={options?.requiredRole}
        requiredPermissions={options?.requiredPermissions}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}