/**
 * Tenant Layout - Wraps all tenant-specific pages
 * Provides tenant context and dashboard layout
 */

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { TenantProvider } from '@/contexts/TenantContext';
import { createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

interface TenantLayoutProps {
  children: React.ReactNode;
  params: { tenant: string };
}

// Generate metadata based on tenant
export async function generateMetadata({ params }: { params: { tenant: string } }): Promise<Metadata> {
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);
  
  try {
    const { data: tenant } = await supabase
      .from('tenants')
      .select('name, description')
      .eq('slug', params.tenant as string)
      .eq('status', 'ACTIVE')
      .single();

    if (!tenant) {
      return {
        title: 'Tenant Not Found - TapDine',
        description: 'The requested restaurant could not be found.',
      };
    }

    return {
      title: `${tenant.name} - TapDine Dashboard`,
      description: tenant.description || `${tenant.name} restaurant management dashboard powered by TapDine`,
      robots: {
        index: false, // Don't index tenant dashboards
        follow: false,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Error - TapDine',
      description: 'An error occurred while loading the restaurant dashboard.',
    };
  }
}

export default async function TenantLayout({ children, params }: TenantLayoutProps) {
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);
  
  try {
    // Fetch tenant data server-side for better performance
    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('slug', params.tenant as string)
      .eq('status', 'ACTIVE')
      .single();

    // Handle tenant not found or inactive
    if (error || !tenant) {
      console.error('Tenant not found:', error);
      notFound();
    }

    return (
      <TenantProvider initialTenant={tenant}>
        <ProtectedRoute>
          <DashboardLayout>
            {children}
          </DashboardLayout>
        </ProtectedRoute>
      </TenantProvider>
    );
  } catch (error) {
    console.error('Error in tenant layout:', error);
    notFound();
  }
}

// Note: generateStaticParams removed because it requires cookies() 
// which is not available during build time. The app will use dynamic rendering instead.