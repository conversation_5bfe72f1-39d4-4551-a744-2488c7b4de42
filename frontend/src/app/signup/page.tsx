/**
 * Tenant Admin Signup Page for TapDine
 * Complete signup flow with validation, Google OAuth, and success handling
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import Image from 'next/image';
import { supabase } from '@/lib/supabase/client';
import { useSlugify } from '@/hooks/useSlugify';
import { Toast } from '@/components/ui/Toast';
import { signupFormSchema, SignupFormData, SignupResponse, SignupError } from '@/lib/validations/signup';

export default function SignupPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({ show: false, message: '', type: 'info' });

  const {
    generateSlug,
    validateSlugAvailability,
    isValidSlugFormat,
    isReservedSlug,
    isValidating,
  } = useSlugify();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    setError,
    clearErrors,
    formState: { errors, isValid },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupFormSchema),
    mode: 'onChange',
    defaultValues: {
      businessName: '',
      tenantSlug: '',
      adminEmail: '',
      password: '',
    },
  });

  const businessName = watch('businessName');
  const tenantSlug = watch('tenantSlug');

  // Auto-generate slug when business name changes
  useEffect(() => {
    if (businessName) {
      const slug = generateSlug(businessName);
      setValue('tenantSlug', slug, { shouldValidate: true });
    }
  }, [businessName, generateSlug, setValue]);

  // Validate slug availability when it changes
  useEffect(() => {
    const validateSlug = async () => {
      if (!tenantSlug || !isValidSlugFormat(tenantSlug)) return;

      if (isReservedSlug(tenantSlug)) {
        setError('tenantSlug', {
          type: 'manual',
          message: 'This slug is reserved and cannot be used',
        });
        return;
      }

      try {
        const isAvailable = await validateSlugAvailability(tenantSlug);
        if (!isAvailable) {
          setError('tenantSlug', {
            type: 'manual',
            message: 'This slug is already taken',
          });
        } else {
          clearErrors('tenantSlug');
        }
      } catch (error) {
        console.error('Slug validation error:', error);
      }
    };

    const debounceTimer = setTimeout(validateSlug, 500);
    return () => clearTimeout(debounceTimer);
  }, [tenantSlug, isValidSlugFormat, isReservedSlug, validateSlugAvailability, setError, clearErrors]);

  // Handle form submission
  const onSubmit = async (data: SignupFormData) => {
    setIsSubmitting(true);
    
    try {
      // Call signup API
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result: SignupResponse | SignupError = await response.json();

      if (!response.ok || !result.success) {
        const errorResult = result as SignupError;
        
        // Handle field-specific errors
        if (errorResult.details) {
          Object.entries(errorResult.details).forEach(([field, messages]) => {
            setError(field as keyof SignupFormData, {
              type: 'manual',
              message: messages[0],
            });
          });
        } else {
          setToast({
            show: true,
            message: errorResult.error || 'Signup failed. Please try again.',
            type: 'error',
          });
        }
        return;
      }

      // Success - store session and redirect
      const successResult = result as SignupResponse;
      if (successResult.data?.session) {
        await supabase.auth.setSession({
          access_token: successResult.data.session.access_token,
          refresh_token: successResult.data.session.refresh_token,
        });
      }

      setToast({
        show: true,
        message: 'Account created successfully! Redirecting to dashboard...',
        type: 'success',
      });

      // Redirect to tenant dashboard
      setTimeout(() => {
        router.push(`/t/${data.tenantSlug}/dashboard`);
      }, 1500);

    } catch (error) {
      console.error('Signup error:', error);
      setToast({
        show: true,
        message: 'An unexpected error occurred. Please try again.',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle Google OAuth signup
  const handleGoogleSignup = async () => {
    setIsGoogleLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback?type=signup`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        setToast({
          show: true,
          message: 'Google signup failed. Please try again.',
          type: 'error',
        });
      }
    } catch (error) {
      console.error('Google signup error:', error);
      setToast({
        show: true,
        message: 'Google signup failed. Please try again.',
        type: 'error',
      });
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <Image
              src="/logo.svg"
              alt="TapDine"
              width={60}
              height={60}
              className="h-15 w-15"
            />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Create your restaurant account
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Join thousands of restaurants using TapDine
          </p>
        </div>

        {/* Google OAuth Button */}
        <div>
          <button
            type="button"
            onClick={handleGoogleSignup}
            disabled={isGoogleLoading}
            className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGoogleLoading ? (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            )}
            {isGoogleLoading ? 'Signing up...' : 'Continue with Google'}
          </button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
              Or continue with email
            </span>
          </div>
        </div>

        {/* Signup Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            {/* Business Name */}
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Business Name *
              </label>
              <Controller
                name="businessName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    id="businessName"
                    type="text"
                    autoComplete="organization"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="Your Restaurant Name"
                  />
                )}
              />
              {errors.businessName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.businessName.message}
                </p>
              )}
            </div>

            {/* Tenant Slug */}
            <div>
              <label htmlFor="tenantSlug" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Restaurant URL *
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm">
                  tapdine.com/
                </span>
                <Controller
                  name="tenantSlug"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      id="tenantSlug"
                      type="text"
                      className="flex-1 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="your-restaurant"
                    />
                  )}
                />
              </div>
              {isValidating && (
                <p className="mt-1 text-sm text-primary-600 dark:text-primary-400">
                  Checking availability...
                </p>
              )}
              {errors.tenantSlug && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.tenantSlug.message}
                </p>
              )}
              {!errors.tenantSlug && tenantSlug && isValidSlugFormat(tenantSlug) && !isReservedSlug(tenantSlug) && (
                <p className="mt-1 text-sm text-green-600 dark:text-green-400">
                  ✓ URL is available
                </p>
              )}
            </div>

            {/* Admin Email */}
            <div>
              <label htmlFor="adminEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Admin Email *
              </label>
              <Controller
                name="adminEmail"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    id="adminEmail"
                    type="email"
                    autoComplete="email"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="<EMAIL>"
                  />
                )}
              />
              {errors.adminEmail && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.adminEmail.message}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password *
              </label>
              <div className="mt-1 relative">
                <Controller
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="new-password"
                      className="block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="Min 8 characters"
                    />
                  )}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? (
                    <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.758 6.758M14.121 14.121l3.121 3.121M14.121 14.121L9.878 9.878m4.242 4.242L19.242 19.242" />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.password.message}
                </p>
              )}
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                Password must contain: uppercase, lowercase, number, and special character
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isSubmitting || !isValid || isValidating}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {isSubmitting ? (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              )}
              {isSubmitting ? 'Creating Account...' : 'Create Account'}
            </button>
          </div>

          {/* Sign In Link */}
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Already have an account?{' '}
              <Link
                href="/login"
                className="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
              >
                Sign in here
              </Link>
            </p>
          </div>
        </form>
      </div>

      {/* Toast Notifications */}
      <Toast
        message={toast.message}
        type={toast.type}
        show={toast.show}
        onClose={() => setToast({ ...toast, show: false })}
      />
    </div>
  );
}