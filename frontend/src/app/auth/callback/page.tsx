/**
 * OAuth callback page for Google authentication
 */

'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the code from URL parameters for OAuth callback
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        
        if (code) {
          // Exchange code for session
          const { data, error } = await supabase.auth.exchangeCodeForSession(code);
          
          if (error) {
            console.error('Code exchange error:', error);
            setStatus('error');
            setMessage('Authentication failed. Please try again.');
            return;
          }

          if (!data.session) {
            setStatus('error');
            setMessage('No session created. Please try again.');
            return;
          }

          const user = data.session.user;
          const authType = searchParams.get('type') || 'login';

          if (authType === 'signup') {
            // Handle OAuth signup flow
            // For OAuth signup, we need to create tenant and user records
            // This would typically involve showing a form to complete the tenant setup
            setStatus('success');
            setMessage('Please complete your restaurant setup...');
            
            // Redirect to tenant setup page
            router.push('/setup');
          } else {
            // Handle OAuth login flow
            // Check if user has existing tenant associations
            const { data: userTenants, error: userError } = await supabase
              .from('user_tenants')
              .select(`
                tenant_id,
                role,
                tenants!inner(slug, name, status)
              `)
              .eq('user_id', user.id)
              .eq('is_active', true)
              .eq('tenants.status', 'active');

            if (userError) {
              console.error('User tenant lookup error:', userError);
              setStatus('error');
              setMessage('Failed to load your account. Please try again.');
              return;
            }

            if (!userTenants || userTenants.length === 0) {
              // User has no tenant associations, redirect to setup
              setStatus('success');
              setMessage('Setting up your account...');
              router.push('/setup');
              return;
            }

            // User has tenants, redirect to first tenant's dashboard
            const firstTenant = userTenants[0];
            setStatus('success');
            setMessage('Redirecting to your dashboard...');
            router.push(`/t/${firstTenant.tenants.slug}/dashboard`);
          }
        } else {
          // No code in URL, try to get existing session
          const { data, error } = await supabase.auth.getSession();
          
          if (error || !data.session) {
            setStatus('error');
            setMessage('Authentication failed. Please try signing in again.');
            return;
          }
          
          // Redirect to dashboard
          setStatus('success');
          setMessage('Redirecting...');
          router.push('/dashboard');
        }

      } catch (error) {
        console.error('Auth callback error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          {status === 'loading' && (
            <div className="flex justify-center mb-4">
              <svg className="animate-spin h-10 w-10 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          )}
          
          {status === 'success' && (
            <div className="flex justify-center mb-4">
              <svg className="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}
          
          {status === 'error' && (
            <div className="flex justify-center mb-4">
              <svg className="h-10 w-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.982 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          )}
          
          <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {message}
          </p>
          
          {status === 'error' && (
            <div className="mt-6 space-y-4">
              <button
                onClick={() => router.push('/login')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Back to Login
              </button>
              
              <div className="text-center">
                <button
                  onClick={() => router.push('/signup')}
                  className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                >
                  Create a new account
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}