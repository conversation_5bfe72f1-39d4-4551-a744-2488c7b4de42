// Service Worker Cleanup Script
// This script unregisters any existing service workers to prevent conflicts

if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      console.log('Unregistering service worker:', registration.scope);
      registration.unregister();
    }
  }).catch(function(error) {
    console.log('Service worker unregistration failed:', error);
  });
}