/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/signup/page";
exports.ids = ["app/signup/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'signup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/signup/page.tsx */ \"(rsc)/./src/app/signup/page.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/signup/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/signup/page\",\n        pathname: \"/signup\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXBkaW5lLWZyb250ZW5kLz9kYzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TenantContext.tsx */ \"(ssr)/./src/contexts/TenantContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmNvbnRleHRzJTJGVGVuYW50Q29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8/NTdiYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9mcm9udGVuZC9zcmMvY29udGV4dHMvVGVuYW50Q29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fsignup%2Fpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fsignup%2Fpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/signup/page.tsx */ \"(ssr)/./src/app/signup/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRnNpZ251cCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RhcGRpbmUtZnJvbnRlbmQvPzAyZGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvc2hpdmEvRGVza3RvcC9CSEVFTURJTkUvZnJvbnRlbmQvc3JjL2FwcC9zaWdudXAvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fsignup%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSlugify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSlugify */ \"(ssr)/./src/hooks/useSlugify.ts\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Toast */ \"(ssr)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _lib_validations_signup__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/validations/signup */ \"(ssr)/./src/lib/validations/signup.ts\");\n/**\n * Tenant Admin Signup Page for TapDine\n * Complete signup flow with validation, Google OAuth, and success handling\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction SignupPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGoogleLoading, setIsGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toast, setToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        message: \"\",\n        type: \"info\"\n    });\n    const { generateSlug, validateSlugAvailability, isValidSlugFormat, isReservedSlug, isValidating } = (0,_hooks_useSlugify__WEBPACK_IMPORTED_MODULE_7__.useSlugify)();\n    const { control, handleSubmit, watch, setValue, setError, clearErrors, formState: { errors, isValid } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(_lib_validations_signup__WEBPACK_IMPORTED_MODULE_9__.signupFormSchema),\n        mode: \"onChange\",\n        defaultValues: {\n            businessName: \"\",\n            tenantSlug: \"\",\n            adminEmail: \"\",\n            password: \"\"\n        }\n    });\n    const businessName = watch(\"businessName\");\n    const tenantSlug = watch(\"tenantSlug\");\n    // Auto-generate slug when business name changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (businessName) {\n            const slug = generateSlug(businessName);\n            setValue(\"tenantSlug\", slug, {\n                shouldValidate: true\n            });\n        }\n    }, [\n        businessName,\n        generateSlug,\n        setValue\n    ]);\n    // Validate slug availability when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const validateSlug = async ()=>{\n            if (!tenantSlug || !isValidSlugFormat(tenantSlug)) return;\n            if (isReservedSlug(tenantSlug)) {\n                setError(\"tenantSlug\", {\n                    type: \"manual\",\n                    message: \"This slug is reserved and cannot be used\"\n                });\n                return;\n            }\n            try {\n                const isAvailable = await validateSlugAvailability(tenantSlug);\n                if (!isAvailable) {\n                    setError(\"tenantSlug\", {\n                        type: \"manual\",\n                        message: \"This slug is already taken\"\n                    });\n                } else {\n                    clearErrors(\"tenantSlug\");\n                }\n            } catch (error) {\n                console.error(\"Slug validation error:\", error);\n            }\n        };\n        const debounceTimer = setTimeout(validateSlug, 500);\n        return ()=>clearTimeout(debounceTimer);\n    }, [\n        tenantSlug,\n        isValidSlugFormat,\n        isReservedSlug,\n        validateSlugAvailability,\n        setError,\n        clearErrors\n    ]);\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            // Call signup API\n            const response = await fetch(\"/api/auth/signup\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            const result = await response.json();\n            if (!response.ok || !result.success) {\n                const errorResult = result;\n                // Handle field-specific errors\n                if (errorResult.details) {\n                    Object.entries(errorResult.details).forEach(([field, messages])=>{\n                        setError(field, {\n                            type: \"manual\",\n                            message: messages[0]\n                        });\n                    });\n                } else {\n                    setToast({\n                        show: true,\n                        message: errorResult.error || \"Signup failed. Please try again.\",\n                        type: \"error\"\n                    });\n                }\n                return;\n            }\n            // Success - store session and redirect\n            const successResult = result;\n            if (successResult.data?.session) {\n                await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.supabase.auth.setSession({\n                    access_token: successResult.data.session.access_token,\n                    refresh_token: successResult.data.session.refresh_token\n                });\n            }\n            setToast({\n                show: true,\n                message: \"Account created successfully! Redirecting to dashboard...\",\n                type: \"success\"\n            });\n            // Redirect to tenant dashboard\n            setTimeout(()=>{\n                router.push(`/t/${data.tenantSlug}/dashboard`);\n            }, 1500);\n        } catch (error) {\n            console.error(\"Signup error:\", error);\n            setToast({\n                show: true,\n                message: \"An unexpected error occurred. Please try again.\",\n                type: \"error\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle Google OAuth signup\n    const handleGoogleSignup = async ()=>{\n        setIsGoogleLoading(true);\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.supabase.auth.signInWithOAuth({\n                provider: \"google\",\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback?type=signup`,\n                    queryParams: {\n                        access_type: \"offline\",\n                        prompt: \"consent\"\n                    }\n                }\n            });\n            if (error) {\n                setToast({\n                    show: true,\n                    message: \"Google signup failed. Please try again.\",\n                    type: \"error\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Google signup error:\", error);\n            setToast({\n                show: true,\n                message: \"Google signup failed. Please try again.\",\n                type: \"error\"\n            });\n        } finally{\n            setIsGoogleLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    src: \"/logo.svg\",\n                                    alt: \"TapDine\",\n                                    width: 60,\n                                    height: 60,\n                                    className: \"h-15 w-15\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: \"Create your restaurant account\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-gray-600 dark:text-gray-400\",\n                                children: \"Join thousands of restaurants using TapDine\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleGoogleSignup,\n                            disabled: isGoogleLoading,\n                            className: \"w-full flex justify-center items-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                isGoogleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 mr-3\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"currentColor\",\n                                            d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"currentColor\",\n                                            d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"currentColor\",\n                                            d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"currentColor\",\n                                            d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                isGoogleLoading ? \"Signing up...\" : \"Continue with Google\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full border-t border-gray-300 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400\",\n                                    children: \"Or continue with email\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        className: \"mt-8 space-y-6\",\n                        onSubmit: handleSubmit(onSubmit),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"businessName\",\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: \"Business Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Controller, {\n                                                name: \"businessName\",\n                                                control: control,\n                                                render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...field,\n                                                        id: \"businessName\",\n                                                        type: \"text\",\n                                                        autoComplete: \"organization\",\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                        placeholder: \"Your Restaurant Name\"\n                                                    }, void 0, false, void 0, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                children: errors.businessName.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tenantSlug\",\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: \"Restaurant URL *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1 flex rounded-md shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm\",\n                                                        children: \"tapdine.com/\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Controller, {\n                                                        name: \"tenantSlug\",\n                                                        control: control,\n                                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ...field,\n                                                                id: \"tenantSlug\",\n                                                                type: \"text\",\n                                                                className: \"flex-1 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                                placeholder: \"your-restaurant\"\n                                                            }, void 0, false, void 0, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            isValidating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-primary-600 dark:text-primary-400\",\n                                                children: \"Checking availability...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.tenantSlug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                children: errors.tenantSlug.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            !errors.tenantSlug && tenantSlug && isValidSlugFormat(tenantSlug) && !isReservedSlug(tenantSlug) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-green-600 dark:text-green-400\",\n                                                children: \"✓ URL is available\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"adminEmail\",\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: \"Admin Email *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Controller, {\n                                                name: \"adminEmail\",\n                                                control: control,\n                                                render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...field,\n                                                        id: \"adminEmail\",\n                                                        type: \"email\",\n                                                        autoComplete: \"email\",\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, void 0, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.adminEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                children: errors.adminEmail.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"password\",\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: \"Password *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Controller, {\n                                                        name: \"password\",\n                                                        control: control,\n                                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ...field,\n                                                                id: \"password\",\n                                                                type: showPassword ? \"text\" : \"password\",\n                                                                autoComplete: \"new-password\",\n                                                                className: \"block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                                placeholder: \"Min 8 characters\"\n                                                            }, void 0, false, void 0, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-gray-400\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-gray-400\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.758 6.758M14.121 14.121l3.121 3.121M14.121 14.121L9.878 9.878m4.242 4.242L19.242 19.242\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-xs text-gray-500 dark:text-gray-400\",\n                                                children: \"Password must contain: uppercase, lowercase, number, and special character\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting || !isValid || isValidating,\n                                    className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\",\n                                    children: [\n                                        isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this),\n                                        isSubmitting ? \"Creating Account...\" : \"Create Account\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Already have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/login\",\n                                            className: \"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\",\n                                            children: \"Sign in here\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_8__.Toast, {\n                message: toast.message,\n                type: toast.type,\n                show: toast.show,\n                onClose: ()=>setToast({\n                        ...toast,\n                        show: false\n                    })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/signup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Toast notification component for success/error messages\n */ /* __next_internal_client_entry_do_not_use__ Toast auto */ \n\nfunction Toast({ message, type, show, onClose, duration = 5000 }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (show) {\n            setIsVisible(true);\n            const timer = setTimeout(()=>{\n                setIsVisible(false);\n                setTimeout(onClose, 300); // Wait for fade out animation\n            }, duration);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        show,\n        duration,\n        onClose\n    ]);\n    if (!show && !isVisible) return null;\n    const bgColor = {\n        success: \"bg-green-50 border-green-200 text-green-800\",\n        error: \"bg-red-50 border-red-200 text-red-800\",\n        info: \"bg-primary-50 border-primary-200 text-primary-800\"\n    }[type];\n    const iconColor = {\n        success: \"text-green-400\",\n        error: \"text-red-400\",\n        info: \"text-primary-400\"\n    }[type];\n    const icon = {\n        success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this),\n        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this),\n        info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed top-4 right-4 z-50 transition-all duration-300 ${isVisible ? \"translate-y-0 opacity-100\" : \"-translate-y-2 opacity-0\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `max-w-sm rounded-lg border p-4 shadow-lg ${bgColor}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex-shrink-0 ${iconColor}`,\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: `inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${iconColor}`,\n                            onClick: ()=>{\n                                setIsVisible(false);\n                                setTimeout(onClose, 300);\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/ui/Toast.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/**\n * Tenant context provider for multi-tenant authentication\n */ /* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction TenantProvider({ children, initialTenant = null }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTenant);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        async function loadTenantAndUser() {\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Get current session\n                const { data: { session }, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (sessionError) throw sessionError;\n                if (!session) {\n                    if (mounted) {\n                        setUser(null);\n                        setTenant(null);\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Get tenant slug from various sources\n                const tenantSlug = getTenantSlug();\n                if (!tenantSlug && !initialTenant) {\n                    // No tenant context available\n                    if (mounted) {\n                        setError(new Error(\"No tenant context available\"));\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Fetch user's tenant data\n                const { data: userData, error: userError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"users\").select(`\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          `).eq(\"id\", session.user.id).single();\n                if (userError) throw userError;\n                // Find the matching tenant\n                const userTenant = userData.user_tenants.find((ut)=>ut.tenant.slug === (tenantSlug || initialTenant?.slug));\n                if (!userTenant) {\n                    throw new Error(\"User does not have access to this tenant\");\n                }\n                if (mounted) {\n                    setUser({\n                        id: userData.id,\n                        email: userData.email,\n                        tenant_id: userTenant.tenant.id,\n                        role: userTenant.role,\n                        first_name: userData.first_name,\n                        last_name: userData.last_name,\n                        avatar_url: userData.avatar_url,\n                        created_at: userData.created_at,\n                        updated_at: userData.updated_at\n                    });\n                    setTenant(userTenant.tenant);\n                    setIsLoading(false);\n                }\n            } catch (err) {\n                console.error(\"Error loading tenant context:\", err);\n                if (mounted) {\n                    setError(err);\n                    setIsLoading(false);\n                }\n            }\n        }\n        loadTenantAndUser();\n        // Subscribe to auth changes\n        const { data: { subscription } } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            if (event === \"SIGNED_IN\" || event === \"TOKEN_REFRESHED\") {\n                loadTenantAndUser();\n            } else if (event === \"SIGNED_OUT\") {\n                setUser(null);\n                setTenant(null);\n                router.push(\"/login\");\n            }\n        });\n        return ()=>{\n            mounted = false;\n            subscription.unsubscribe();\n        };\n    }, [\n        router,\n        initialTenant\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            user,\n            isLoading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Hook to use tenant context\n */ function useTenant() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (context === undefined) {\n        throw new Error(\"useTenant must be used within a TenantProvider\");\n    }\n    return context;\n}\n/**\n * Get tenant slug from various sources\n */ function getTenantSlug() {\n    if (true) return null;\n    // 1. Check subdomain\n    const hostname = window.location.hostname;\n    const subdomain = hostname.split(\".\")[0];\n    if (subdomain && subdomain !== \"www\" && subdomain !== \"localhost\") {\n        return subdomain;\n    }\n    // 2. Check URL path\n    const pathMatch = window.location.pathname.match(/^\\/t\\/([^\\/]+)/);\n    if (pathMatch) {\n        return pathMatch[1];\n    }\n    // 3. Check query parameter\n    const params = new URLSearchParams(window.location.search);\n    const tenantParam = params.get(\"tenant\");\n    if (tenantParam) {\n        return tenantParam;\n    }\n    // 4. Check cookie\n    const tenantCookie = document.cookie.split(\"; \").find((row)=>row.startsWith(\"tenant-slug=\"));\n    if (tenantCookie) {\n        return tenantCookie.split(\"=\")[1];\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSlugify.ts":
/*!*********************************!*\
  !*** ./src/hooks/useSlugify.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSlugify: () => (/* binding */ useSlugify)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Hook for generating and validating URL slugs\n * Converts business names to URL-friendly slugs\n */ /* __next_internal_client_entry_do_not_use__ useSlugify auto */ \nfunction useSlugify() {\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Generate slug from business name\n    const generateSlug = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((businessName)=>{\n        return businessName.toLowerCase().trim().replace(/[^a-z0-9\\s-]/g, \"\") // Remove special characters\n        .replace(/\\s+/g, \"-\") // Replace spaces with hyphens\n        .replace(/-+/g, \"-\") // Replace multiple hyphens with single\n        .replace(/^-|-$/g, \"\"); // Remove leading/trailing hyphens\n    }, []);\n    // Validate slug availability (simulated API call)\n    const validateSlugAvailability = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (slug)=>{\n        if (!slug || slug.length < 3) return false;\n        setIsValidating(true);\n        try {\n            // Simulate API call to check slug availability\n            const response = await fetch(`/api/auth/validate-slug?slug=${encodeURIComponent(slug)}`);\n            const data = await response.json();\n            return data.available;\n        } catch (error) {\n            console.error(\"Slug validation error:\", error);\n            return false;\n        } finally{\n            setIsValidating(false);\n        }\n    }, []);\n    // Check if slug is valid format\n    const isValidSlugFormat = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((slug)=>{\n        const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;\n        return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 50;\n    }, []);\n    // Reserved slugs that cannot be used\n    const reservedSlugs = [\n        \"api\",\n        \"admin\",\n        \"app\",\n        \"www\",\n        \"mail\",\n        \"ftp\",\n        \"localhost\",\n        \"dashboard\",\n        \"billing\",\n        \"support\",\n        \"help\",\n        \"docs\",\n        \"blog\",\n        \"status\",\n        \"staging\",\n        \"dev\",\n        \"test\",\n        \"demo\",\n        \"cdn\",\n        \"assets\",\n        \"static\",\n        \"public\",\n        \"private\",\n        \"secure\",\n        \"internal\",\n        \"system\",\n        \"root\",\n        \"user\",\n        \"account\",\n        \"login\",\n        \"signup\",\n        \"auth\",\n        \"oauth\",\n        \"settings\",\n        \"profile\",\n        \"home\",\n        \"index\",\n        \"about\",\n        \"contact\",\n        \"terms\",\n        \"privacy\",\n        \"legal\",\n        \"pricing\",\n        \"features\",\n        \"enterprise\"\n    ];\n    const isReservedSlug = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((slug)=>{\n        return reservedSlugs.includes(slug.toLowerCase());\n    }, []);\n    return {\n        generateSlug,\n        validateSlugAvailability,\n        isValidSlugFormat,\n        isReservedSlug,\n        isValidating\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSlugify.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   getTenantSupabaseClient: () => (/* binding */ getTenantSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/**\n * Supabase client configuration with multi-tenant support\n */ \nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\n// Debug logging to verify configuration\nconsole.log(\"\\uD83D\\uDD0D Supabase Config Check:\");\nconsole.log(\"URL:\", supabaseUrl);\nconsole.log(\"Key:\", supabaseKey ? `${supabaseKey.substring(0, 20)}...` : \"NOT SET\");\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseKey);\n// Legacy function for backward compatibility\nfunction getSupabaseClient() {\n    return createClient();\n}\n// Helper to get tenant-scoped Supabase client\nfunction getTenantSupabaseClient(tenantId) {\n    const client = getSupabaseClient();\n    // Add tenant context to all queries\n    client.auth.onAuthStateChange((event, session)=>{\n        if (session) {\n            // Set tenant context in headers for RLS\n            client.rest.headers[\"x-tenant-id\"] = tenantId;\n        }\n    });\n    return client;\n}\n// Export the default client\nconst supabase = getSupabaseClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/validations/signup.ts":
/*!***************************************!*\
  !*** ./src/lib/validations/signup.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   businessNameSchema: () => (/* binding */ businessNameSchema),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   passwordSchema: () => (/* binding */ passwordSchema),\n/* harmony export */   signupErrorSchema: () => (/* binding */ signupErrorSchema),\n/* harmony export */   signupFormSchema: () => (/* binding */ signupFormSchema),\n/* harmony export */   signupResponseSchema: () => (/* binding */ signupResponseSchema),\n/* harmony export */   tenantSlugSchema: () => (/* binding */ tenantSlugSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/**\n * Zod validation schemas for tenant admin signup\n */ \n// Business name validation\nconst businessNameSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, \"Business name must be at least 2 characters\").max(100, \"Business name cannot exceed 100 characters\").regex(/^[a-zA-Z0-9\\s\\-'&.]+$/, \"Business name contains invalid characters\").transform((val)=>val.trim());\n// Tenant slug validation\nconst tenantSlugSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(3, \"Tenant slug must be at least 3 characters\").max(50, \"Tenant slug cannot exceed 50 characters\").regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, \"Slug can only contain lowercase letters, numbers, and hyphens\").refine((val)=>!val.startsWith(\"-\") && !val.endsWith(\"-\"), \"Slug cannot start or end with hyphen\").refine((val)=>!val.includes(\"--\"), \"Slug cannot contain consecutive hyphens\").transform((val)=>val.toLowerCase().trim());\n// Email validation\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Please enter a valid email address\").max(255, \"Email cannot exceed 255 characters\").transform((val)=>val.toLowerCase().trim());\n// Password validation\nconst passwordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Password must be at least 8 characters\").max(128, \"Password cannot exceed 128 characters\").regex(/[A-Z]/, \"Password must contain at least one uppercase letter\").regex(/[a-z]/, \"Password must contain at least one lowercase letter\").regex(/\\d/, \"Password must contain at least one number\").regex(/[!@#$%^&*(),.?\":{}|<>]/, \"Password must contain at least one special character\");\n// Complete signup form schema\nconst signupFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    businessName: businessNameSchema,\n    tenantSlug: tenantSlugSchema,\n    adminEmail: emailSchema,\n    password: passwordSchema\n});\n// API response schemas\nconst signupResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    success: zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        tenant: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            slug: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n        }),\n        user: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            email: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n        }),\n        session: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            access_token: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            refresh_token: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            expires_in: zod__WEBPACK_IMPORTED_MODULE_0__.number()\n        })\n    }).optional()\n});\nconst signupErrorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    success: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    details: zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())).optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/validations/signup.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37cd149362fb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGJhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3Y2QxNDkzNjJmYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(rsc)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/**\n * Root layout with multi-tenant authentication\n */ \n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__.TenantProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7Q0FFQztBQUV5RDtBQUNuQztBQUVSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ0wsbUVBQWNBOzBCQUNaRTs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUm9vdCBsYXlvdXQgd2l0aCBtdWx0aS10ZW5hbnQgYXV0aGVudGljYXRpb25cbiAqL1xuXG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFRlbmFudFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59Il0sIm5hbWVzIjpbIlRlbmFudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * 404 Not Found page\n */ \n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                            children: \"The page you're looking for doesn't exist or the tenant is not active.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                            children: \"Go to Home\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/login\",\n                                className: \"text-primary-600 hover:text-primary-500 text-sm font-medium\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/signup/page.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/signup/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ e0),\n/* harmony export */   useTenant: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#TenantProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#useTenant`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/TenantContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/zod","vendor-chunks/@swc","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();