/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/validate-slug/route";
exports.ids = ["app/api/auth/validate-slug/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fvalidate-slug%2Froute&page=%2Fapi%2Fauth%2Fvalidate-slug%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fvalidate-slug%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fvalidate-slug%2Froute&page=%2Fapi%2Fauth%2Fvalidate-slug%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fvalidate-slug%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_validate_slug_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/auth/validate-slug/route.ts */ \"(rsc)/./src/app/api/auth/validate-slug/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/validate-slug/route\",\n        pathname: \"/api/auth/validate-slug\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/validate-slug/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/api/auth/validate-slug/route.ts\",\n    nextConfigOutput,\n    userland: _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_validate_slug_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/validate-slug/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fvalidate-slug%2Froute&page=%2Fapi%2Fauth%2Fvalidate-slug%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fvalidate-slug%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/validate-slug/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/validate-slug/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/**\n * API route to validate tenant slug availability\n */ \n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const slug = searchParams.get(\"slug\");\n        if (!slug) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Slug parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate slug format\n        const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;\n        if (!slugRegex.test(slug) || slug.length < 3 || slug.length > 50) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                available: false,\n                error: \"Invalid slug format\"\n            }, {\n                status: 400\n            });\n        }\n        // Check reserved slugs\n        const reservedSlugs = [\n            \"api\",\n            \"admin\",\n            \"app\",\n            \"www\",\n            \"mail\",\n            \"ftp\",\n            \"localhost\",\n            \"dashboard\",\n            \"billing\",\n            \"support\",\n            \"help\",\n            \"docs\",\n            \"blog\",\n            \"status\",\n            \"staging\",\n            \"dev\",\n            \"test\",\n            \"demo\",\n            \"cdn\",\n            \"assets\",\n            \"static\",\n            \"public\",\n            \"private\",\n            \"secure\",\n            \"internal\",\n            \"system\",\n            \"root\",\n            \"user\",\n            \"account\",\n            \"login\",\n            \"signup\",\n            \"auth\",\n            \"oauth\",\n            \"settings\",\n            \"profile\",\n            \"home\",\n            \"index\",\n            \"about\",\n            \"contact\",\n            \"terms\",\n            \"privacy\",\n            \"legal\",\n            \"pricing\",\n            \"features\",\n            \"enterprise\"\n        ];\n        if (reservedSlugs.includes(slug.toLowerCase())) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                available: false,\n                error: \"Slug is reserved\"\n            }, {\n                status: 400\n            });\n        }\n        // Check database for existing slug\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServiceClient)();\n        const { data, error } = await supabase.from(\"tenants\").select(\"slug\").eq(\"slug\", slug).single();\n        if (error && error.code !== \"PGRST116\") {\n            // PGRST116 is \"not found\" error, which is what we want\n            console.error(\"Database error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Database error\"\n            }, {\n                status: 500\n            });\n        }\n        // If data exists, slug is taken\n        const available = !data;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            available,\n            slug\n        });\n    } catch (error) {\n        console.error(\"Slug validation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/validate-slug/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createSupabaseServerClient: () => (/* binding */ createSupabaseServerClient),\n/* harmony export */   createSupabaseServiceClient: () => (/* binding */ createSupabaseServiceClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Server-side Supabase client for Next.js with multi-tenant support\n */ \n\nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\nconst createClient = (cookieStore)=>{\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>{\n                        cookieStore.set(name, value, options);\n                    });\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Legacy function for backward compatibility\nasync function createSupabaseServerClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return createClient(cookieStore);\n}\n// Service role client for admin operations\nfunction createSupabaseServiceClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://cgzcndxnfldupgdddnra.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // Service role client doesn't need cookies\n            }\n        },\n        auth: {\n            persistSession: false,\n            autoRefreshToken: false,\n            detectSessionInUrl: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fvalidate-slug%2Froute&page=%2Fapi%2Fauth%2Fvalidate-slug%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fvalidate-slug%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();