/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/signup/route";
exports.ids = ["app/api/auth/signup/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_signup_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/auth/signup/route.ts */ \"(rsc)/./src/app/api/auth/signup/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/signup/route\",\n        pathname: \"/api/auth/signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/signup/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/api/auth/signup/route.ts\",\n    nextConfigOutput,\n    userland: _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_signup_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/signup/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/signup/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/auth/signup/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_validations_signup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/validations/signup */ \"(rsc)/./src/lib/validations/signup.ts\");\n/**\n * API route for tenant admin signup\n * Creates tenant, admin user, and returns session\n */ \n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate request body\n        const validation = _lib_validations_signup__WEBPACK_IMPORTED_MODULE_2__.signupFormSchema.safeParse(body);\n        if (!validation.success) {\n            const fieldErrors = {};\n            if (validation.error?.errors) {\n                validation.error.errors.forEach((error)=>{\n                    const field = error.path[0];\n                    if (!fieldErrors[field]) {\n                        fieldErrors[field] = [];\n                    }\n                    fieldErrors[field].push(error.message);\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Validation failed\",\n                details: fieldErrors\n            }, {\n                status: 400\n            });\n        }\n        const { businessName, tenantSlug, adminEmail, password } = validation.data;\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServiceClient)();\n        // Check if slug is already taken\n        const { data: existingTenant } = await supabase.from(\"tenants\").select(\"slug\").eq(\"slug\", tenantSlug).single();\n        if (existingTenant) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Validation failed\",\n                details: {\n                    tenantSlug: [\n                        \"This slug is already taken\"\n                    ]\n                }\n            }, {\n                status: 400\n            });\n        }\n        // Check if email is already taken\n        const { data: existingUser } = await supabase.from(\"users\").select(\"email\").eq(\"email\", adminEmail).single();\n        if (existingUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Validation failed\",\n                details: {\n                    adminEmail: [\n                        \"This email is already registered\"\n                    ]\n                }\n            }, {\n                status: 400\n            });\n        }\n        // Create Supabase auth user\n        const { data: authData, error: authError } = await supabase.auth.admin.createUser({\n            email: adminEmail,\n            password: password,\n            email_confirm: true\n        });\n        if (authError || !authData.user) {\n            console.error(\"Auth user creation error:\", authError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create user account\"\n            }, {\n                status: 500\n            });\n        }\n        // Create tenant\n        const { data: tenant, error: tenantError } = await supabase.from(\"tenants\").insert({\n            name: businessName,\n            slug: tenantSlug,\n            email: adminEmail,\n            status: \"active\"\n        }).select().single();\n        if (tenantError || !tenant) {\n            console.error(\"Tenant creation error:\", tenantError);\n            // Clean up auth user if tenant creation fails\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create tenant\"\n            }, {\n                status: 500\n            });\n        }\n        // Create user profile\n        const { data: user, error: userError } = await supabase.from(\"users\").insert({\n            id: authData.user.id,\n            email: adminEmail,\n            first_name: businessName\n        }).select().single();\n        if (userError || !user) {\n            console.error(\"User profile creation error:\", userError);\n            // Clean up auth user and tenant\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            await supabase.from(\"tenants\").delete().eq(\"id\", tenant.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create user profile\"\n            }, {\n                status: 500\n            });\n        }\n        // Create user-tenant association\n        const { error: associationError } = await supabase.from(\"user_tenants\").insert({\n            user_id: authData.user.id,\n            tenant_id: tenant.id,\n            role: \"owner\",\n            is_active: true\n        });\n        if (associationError) {\n            console.error(\"User-tenant association error:\", associationError);\n            // Clean up created records\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            await supabase.from(\"tenants\").delete().eq(\"id\", tenant.id);\n            await supabase.from(\"users\").delete().eq(\"id\", user.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create account association\"\n            }, {\n                status: 500\n            });\n        }\n        // Generate session tokens\n        const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({\n            type: \"signup\",\n            email: adminEmail\n        });\n        if (sessionError) {\n            console.error(\"Session generation error:\", sessionError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to generate session\"\n            }, {\n                status: 500\n            });\n        }\n        // Create a proper session\n        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({\n            email: adminEmail,\n            password: password\n        });\n        let sessionTokens = null;\n        if (signInData.session) {\n            sessionTokens = {\n                access_token: signInData.session.access_token,\n                refresh_token: signInData.session.refresh_token,\n                expires_in: signInData.session.expires_in || 3600\n            };\n        }\n        // Return success response\n        const response = {\n            success: true,\n            message: \"Account created successfully\",\n            data: {\n                tenant: {\n                    id: tenant.id,\n                    name: tenant.name,\n                    slug: tenant.slug\n                },\n                user: {\n                    id: user.id,\n                    email: user.email\n                },\n                session: sessionTokens\n            }\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Signup error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3NpZ251cC9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7OztDQUdDLEdBRXVEO0FBQ1k7QUFDYztBQUUzRSxlQUFlRyxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsT0FBTyxNQUFNRCxRQUFRRSxJQUFJO1FBRS9CLHdCQUF3QjtRQUN4QixNQUFNQyxhQUFhTCxxRUFBZ0JBLENBQUNNLFNBQVMsQ0FBQ0g7UUFDOUMsSUFBSSxDQUFDRSxXQUFXRSxPQUFPLEVBQUU7WUFDdkIsTUFBTUMsY0FBd0MsQ0FBQztZQUMvQyxJQUFJSCxXQUFXSSxLQUFLLEVBQUVDLFFBQVE7Z0JBQzVCTCxXQUFXSSxLQUFLLENBQUNDLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDRixDQUFBQTtvQkFDOUIsTUFBTUcsUUFBUUgsTUFBTUksSUFBSSxDQUFDLEVBQUU7b0JBQzNCLElBQUksQ0FBQ0wsV0FBVyxDQUFDSSxNQUFNLEVBQUU7d0JBQ3ZCSixXQUFXLENBQUNJLE1BQU0sR0FBRyxFQUFFO29CQUN6QjtvQkFDQUosV0FBVyxDQUFDSSxNQUFNLENBQUNFLElBQUksQ0FBQ0wsTUFBTU0sT0FBTztnQkFDdkM7WUFDRjtZQUVBLE9BQU9qQixrRkFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFDRUcsU0FBUztnQkFDVEUsT0FBTztnQkFDUE8sU0FBU1I7WUFDWCxHQUNBO2dCQUFFUyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNLEVBQUVDLFlBQVksRUFBRUMsVUFBVSxFQUFFQyxVQUFVLEVBQUVDLFFBQVEsRUFBRSxHQUFHaEIsV0FBV2lCLElBQUk7UUFDMUUsTUFBTUMsV0FBV3hCLGlGQUEyQkE7UUFFNUMsaUNBQWlDO1FBQ2pDLE1BQU0sRUFBRXVCLE1BQU1FLGNBQWMsRUFBRSxHQUFHLE1BQU1ELFNBQ3BDRSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLFFBQ1BDLEVBQUUsQ0FBQyxRQUFRUixZQUNYUyxNQUFNO1FBRVQsSUFBSUosZ0JBQWdCO1lBQ2xCLE9BQU8xQixrRkFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFDRUcsU0FBUztnQkFDVEUsT0FBTztnQkFDUE8sU0FBUztvQkFDUEcsWUFBWTt3QkFBQztxQkFBNkI7Z0JBQzVDO1lBQ0YsR0FDQTtnQkFBRUYsUUFBUTtZQUFJO1FBRWxCO1FBRUEsa0NBQWtDO1FBQ2xDLE1BQU0sRUFBRUssTUFBTU8sWUFBWSxFQUFFLEdBQUcsTUFBTU4sU0FDbENFLElBQUksQ0FBQyxTQUNMQyxNQUFNLENBQUMsU0FDUEMsRUFBRSxDQUFDLFNBQVNQLFlBQ1pRLE1BQU07UUFFVCxJQUFJQyxjQUFjO1lBQ2hCLE9BQU8vQixrRkFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFDRUcsU0FBUztnQkFDVEUsT0FBTztnQkFDUE8sU0FBUztvQkFDUEksWUFBWTt3QkFBQztxQkFBbUM7Z0JBQ2xEO1lBQ0YsR0FDQTtnQkFBRUgsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU0sRUFBRUssTUFBTVEsUUFBUSxFQUFFckIsT0FBT3NCLFNBQVMsRUFBRSxHQUFHLE1BQU1SLFNBQVNTLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxVQUFVLENBQUM7WUFDaEZDLE9BQU9mO1lBQ1BDLFVBQVVBO1lBQ1ZlLGVBQWU7UUFDakI7UUFFQSxJQUFJTCxhQUFhLENBQUNELFNBQVNPLElBQUksRUFBRTtZQUMvQkMsUUFBUTdCLEtBQUssQ0FBQyw2QkFBNkJzQjtZQUMzQyxPQUFPakMsa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VHLFNBQVM7Z0JBQ1RFLE9BQU87WUFDVCxHQUNBO2dCQUFFUSxRQUFRO1lBQUk7UUFFbEI7UUFFQSxnQkFBZ0I7UUFDaEIsTUFBTSxFQUFFSyxNQUFNaUIsTUFBTSxFQUFFOUIsT0FBTytCLFdBQVcsRUFBRSxHQUFHLE1BQU1qQixTQUNoREUsSUFBSSxDQUFDLFdBQ0xnQixNQUFNLENBQUM7WUFDTkMsTUFBTXhCO1lBQ055QixNQUFNeEI7WUFDTmdCLE9BQU9mO1lBQ1BILFFBQVE7UUFDVixHQUNDUyxNQUFNLEdBQ05FLE1BQU07UUFFVCxJQUFJWSxlQUFlLENBQUNELFFBQVE7WUFDMUJELFFBQVE3QixLQUFLLENBQUMsMEJBQTBCK0I7WUFFeEMsOENBQThDO1lBQzlDLE1BQU1qQixTQUFTUyxJQUFJLENBQUNDLEtBQUssQ0FBQ1csVUFBVSxDQUFDZCxTQUFTTyxJQUFJLENBQUNRLEVBQUU7WUFFckQsT0FBTy9DLGtGQUFZQSxDQUFDTSxJQUFJLENBQ3RCO2dCQUNFRyxTQUFTO2dCQUNURSxPQUFPO1lBQ1QsR0FDQTtnQkFBRVEsUUFBUTtZQUFJO1FBRWxCO1FBRUEsc0JBQXNCO1FBQ3RCLE1BQU0sRUFBRUssTUFBTWUsSUFBSSxFQUFFNUIsT0FBT3FDLFNBQVMsRUFBRSxHQUFHLE1BQU12QixTQUM1Q0UsSUFBSSxDQUFDLFNBQ0xnQixNQUFNLENBQUM7WUFDTkksSUFBSWYsU0FBU08sSUFBSSxDQUFDUSxFQUFFO1lBQ3BCVixPQUFPZjtZQUNQMkIsWUFBWTdCO1FBQ2QsR0FDQ1EsTUFBTSxHQUNORSxNQUFNO1FBRVQsSUFBSWtCLGFBQWEsQ0FBQ1QsTUFBTTtZQUN0QkMsUUFBUTdCLEtBQUssQ0FBQyxnQ0FBZ0NxQztZQUU5QyxnQ0FBZ0M7WUFDaEMsTUFBTXZCLFNBQVNTLElBQUksQ0FBQ0MsS0FBSyxDQUFDVyxVQUFVLENBQUNkLFNBQVNPLElBQUksQ0FBQ1EsRUFBRTtZQUNyRCxNQUFNdEIsU0FBU0UsSUFBSSxDQUFDLFdBQVd1QixNQUFNLEdBQUdyQixFQUFFLENBQUMsTUFBTVksT0FBT00sRUFBRTtZQUUxRCxPQUFPL0Msa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VHLFNBQVM7Z0JBQ1RFLE9BQU87WUFDVCxHQUNBO2dCQUFFUSxRQUFRO1lBQUk7UUFFbEI7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTSxFQUFFUixPQUFPd0MsZ0JBQWdCLEVBQUUsR0FBRyxNQUFNMUIsU0FDdkNFLElBQUksQ0FBQyxnQkFDTGdCLE1BQU0sQ0FBQztZQUNOUyxTQUFTcEIsU0FBU08sSUFBSSxDQUFDUSxFQUFFO1lBQ3pCTSxXQUFXWixPQUFPTSxFQUFFO1lBQ3BCTyxNQUFNO1lBQ05DLFdBQVc7UUFDYjtRQUVGLElBQUlKLGtCQUFrQjtZQUNwQlgsUUFBUTdCLEtBQUssQ0FBQyxrQ0FBa0N3QztZQUVoRCwyQkFBMkI7WUFDM0IsTUFBTTFCLFNBQVNTLElBQUksQ0FBQ0MsS0FBSyxDQUFDVyxVQUFVLENBQUNkLFNBQVNPLElBQUksQ0FBQ1EsRUFBRTtZQUNyRCxNQUFNdEIsU0FBU0UsSUFBSSxDQUFDLFdBQVd1QixNQUFNLEdBQUdyQixFQUFFLENBQUMsTUFBTVksT0FBT00sRUFBRTtZQUMxRCxNQUFNdEIsU0FBU0UsSUFBSSxDQUFDLFNBQVN1QixNQUFNLEdBQUdyQixFQUFFLENBQUMsTUFBTVUsS0FBS1EsRUFBRTtZQUV0RCxPQUFPL0Msa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VHLFNBQVM7Z0JBQ1RFLE9BQU87WUFDVCxHQUNBO2dCQUFFUSxRQUFRO1lBQUk7UUFFbEI7UUFFQSwwQkFBMEI7UUFDMUIsTUFBTSxFQUFFSyxNQUFNZ0MsV0FBVyxFQUFFN0MsT0FBTzhDLFlBQVksRUFBRSxHQUFHLE1BQU1oQyxTQUFTUyxJQUFJLENBQUNDLEtBQUssQ0FBQ3VCLFlBQVksQ0FBQztZQUN4RkMsTUFBTTtZQUNOdEIsT0FBT2Y7UUFDVDtRQUVBLElBQUltQyxjQUFjO1lBQ2hCakIsUUFBUTdCLEtBQUssQ0FBQyw2QkFBNkI4QztZQUMzQyxPQUFPekQsa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VHLFNBQVM7Z0JBQ1RFLE9BQU87WUFDVCxHQUNBO2dCQUFFUSxRQUFRO1lBQUk7UUFFbEI7UUFFQSwwQkFBMEI7UUFDMUIsTUFBTSxFQUFFSyxNQUFNb0MsVUFBVSxFQUFFakQsT0FBT2tELFdBQVcsRUFBRSxHQUFHLE1BQU1wQyxTQUFTUyxJQUFJLENBQUM0QixrQkFBa0IsQ0FBQztZQUN0RnpCLE9BQU9mO1lBQ1BDLFVBQVVBO1FBQ1o7UUFFQSxJQUFJd0MsZ0JBQWdCO1FBQ3BCLElBQUlILFdBQVdJLE9BQU8sRUFBRTtZQUN0QkQsZ0JBQWdCO2dCQUNkRSxjQUFjTCxXQUFXSSxPQUFPLENBQUNDLFlBQVk7Z0JBQzdDQyxlQUFlTixXQUFXSSxPQUFPLENBQUNFLGFBQWE7Z0JBQy9DQyxZQUFZUCxXQUFXSSxPQUFPLENBQUNHLFVBQVUsSUFBSTtZQUMvQztRQUNGO1FBRUEsMEJBQTBCO1FBQzFCLE1BQU1DLFdBQVc7WUFDZjNELFNBQVM7WUFDVFEsU0FBUztZQUNUTyxNQUFNO2dCQUNKaUIsUUFBUTtvQkFDTk0sSUFBSU4sT0FBT00sRUFBRTtvQkFDYkgsTUFBTUgsT0FBT0csSUFBSTtvQkFDakJDLE1BQU1KLE9BQU9JLElBQUk7Z0JBQ25CO2dCQUNBTixNQUFNO29CQUNKUSxJQUFJUixLQUFLUSxFQUFFO29CQUNYVixPQUFPRSxLQUFLRixLQUFLO2dCQUNuQjtnQkFDQTJCLFNBQVNEO1lBQ1g7UUFDRjtRQUVBLE9BQU8vRCxrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDOEQsVUFBVTtZQUFFakQsUUFBUTtRQUFJO0lBRW5ELEVBQUUsT0FBT1IsT0FBTztRQUNkNkIsUUFBUTdCLEtBQUssQ0FBQyxpQkFBaUJBO1FBQy9CLE9BQU9YLGtGQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQ0VHLFNBQVM7WUFDVEUsT0FBTztRQUNULEdBQ0E7WUFBRVEsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXBkaW5lLWZyb250ZW5kLy4vc3JjL2FwcC9hcGkvYXV0aC9zaWdudXAvcm91dGUudHM/ZmYwOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFQSSByb3V0ZSBmb3IgdGVuYW50IGFkbWluIHNpZ251cFxuICogQ3JlYXRlcyB0ZW5hbnQsIGFkbWluIHVzZXIsIGFuZCByZXR1cm5zIHNlc3Npb25cbiAqL1xuXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlU3VwYWJhc2VTZXJ2aWNlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJztcbmltcG9ydCB7IHNpZ251cEZvcm1TY2hlbWEsIHNpZ251cFJlc3BvbnNlU2NoZW1hIH0gZnJvbSAnQC9saWIvdmFsaWRhdGlvbnMvc2lnbnVwJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgXG4gICAgLy8gVmFsaWRhdGUgcmVxdWVzdCBib2R5XG4gICAgY29uc3QgdmFsaWRhdGlvbiA9IHNpZ251cEZvcm1TY2hlbWEuc2FmZVBhcnNlKGJvZHkpO1xuICAgIGlmICghdmFsaWRhdGlvbi5zdWNjZXNzKSB7XG4gICAgICBjb25zdCBmaWVsZEVycm9yczogUmVjb3JkPHN0cmluZywgc3RyaW5nW10+ID0ge307XG4gICAgICBpZiAodmFsaWRhdGlvbi5lcnJvcj8uZXJyb3JzKSB7XG4gICAgICAgIHZhbGlkYXRpb24uZXJyb3IuZXJyb3JzLmZvckVhY2goZXJyb3IgPT4ge1xuICAgICAgICAgIGNvbnN0IGZpZWxkID0gZXJyb3IucGF0aFswXSBhcyBzdHJpbmc7XG4gICAgICAgICAgaWYgKCFmaWVsZEVycm9yc1tmaWVsZF0pIHtcbiAgICAgICAgICAgIGZpZWxkRXJyb3JzW2ZpZWxkXSA9IFtdO1xuICAgICAgICAgIH1cbiAgICAgICAgICBmaWVsZEVycm9yc1tmaWVsZF0ucHVzaChlcnJvci5tZXNzYWdlKTtcbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnVmFsaWRhdGlvbiBmYWlsZWQnLFxuICAgICAgICAgIGRldGFpbHM6IGZpZWxkRXJyb3JzLFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgeyBidXNpbmVzc05hbWUsIHRlbmFudFNsdWcsIGFkbWluRW1haWwsIHBhc3N3b3JkIH0gPSB2YWxpZGF0aW9uLmRhdGE7XG4gICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZVNlcnZpY2VDbGllbnQoKTtcblxuICAgIC8vIENoZWNrIGlmIHNsdWcgaXMgYWxyZWFkeSB0YWtlblxuICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdUZW5hbnQgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndGVuYW50cycpXG4gICAgICAuc2VsZWN0KCdzbHVnJylcbiAgICAgIC5lcSgnc2x1ZycsIHRlbmFudFNsdWcpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXhpc3RpbmdUZW5hbnQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnVmFsaWRhdGlvbiBmYWlsZWQnLFxuICAgICAgICAgIGRldGFpbHM6IHtcbiAgICAgICAgICAgIHRlbmFudFNsdWc6IFsnVGhpcyBzbHVnIGlzIGFscmVhZHkgdGFrZW4nXSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgZW1haWwgaXMgYWxyZWFkeSB0YWtlblxuICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdVc2VyIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgIC5zZWxlY3QoJ2VtYWlsJylcbiAgICAgIC5lcSgnZW1haWwnLCBhZG1pbkVtYWlsKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGV4aXN0aW5nVXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdWYWxpZGF0aW9uIGZhaWxlZCcsXG4gICAgICAgICAgZGV0YWlsczoge1xuICAgICAgICAgICAgYWRtaW5FbWFpbDogWydUaGlzIGVtYWlsIGlzIGFscmVhZHkgcmVnaXN0ZXJlZCddLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgU3VwYWJhc2UgYXV0aCB1c2VyXG4gICAgY29uc3QgeyBkYXRhOiBhdXRoRGF0YSwgZXJyb3I6IGF1dGhFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5hZG1pbi5jcmVhdGVVc2VyKHtcbiAgICAgIGVtYWlsOiBhZG1pbkVtYWlsLFxuICAgICAgcGFzc3dvcmQ6IHBhc3N3b3JkLFxuICAgICAgZW1haWxfY29uZmlybTogdHJ1ZSxcbiAgICB9KTtcblxuICAgIGlmIChhdXRoRXJyb3IgfHwgIWF1dGhEYXRhLnVzZXIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggdXNlciBjcmVhdGlvbiBlcnJvcjonLCBhdXRoRXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIHVzZXIgYWNjb3VudCcsXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgdGVuYW50XG4gICAgY29uc3QgeyBkYXRhOiB0ZW5hbnQsIGVycm9yOiB0ZW5hbnRFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0ZW5hbnRzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICBuYW1lOiBidXNpbmVzc05hbWUsXG4gICAgICAgIHNsdWc6IHRlbmFudFNsdWcsXG4gICAgICAgIGVtYWlsOiBhZG1pbkVtYWlsLFxuICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgfSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHRlbmFudEVycm9yIHx8ICF0ZW5hbnQpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RlbmFudCBjcmVhdGlvbiBlcnJvcjonLCB0ZW5hbnRFcnJvcik7XG4gICAgICBcbiAgICAgIC8vIENsZWFuIHVwIGF1dGggdXNlciBpZiB0ZW5hbnQgY3JlYXRpb24gZmFpbHNcbiAgICAgIGF3YWl0IHN1cGFiYXNlLmF1dGguYWRtaW4uZGVsZXRlVXNlcihhdXRoRGF0YS51c2VyLmlkKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIHRlbmFudCcsXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgdXNlciBwcm9maWxlXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICBpZDogYXV0aERhdGEudXNlci5pZCxcbiAgICAgICAgZW1haWw6IGFkbWluRW1haWwsXG4gICAgICAgIGZpcnN0X25hbWU6IGJ1c2luZXNzTmFtZSwgLy8gVXNlIGJ1c2luZXNzIG5hbWUgYXMgZmlyc3QgbmFtZSBmb3Igbm93XG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAodXNlckVycm9yIHx8ICF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVc2VyIHByb2ZpbGUgY3JlYXRpb24gZXJyb3I6JywgdXNlckVycm9yKTtcbiAgICAgIFxuICAgICAgLy8gQ2xlYW4gdXAgYXV0aCB1c2VyIGFuZCB0ZW5hbnRcbiAgICAgIGF3YWl0IHN1cGFiYXNlLmF1dGguYWRtaW4uZGVsZXRlVXNlcihhdXRoRGF0YS51c2VyLmlkKTtcbiAgICAgIGF3YWl0IHN1cGFiYXNlLmZyb20oJ3RlbmFudHMnKS5kZWxldGUoKS5lcSgnaWQnLCB0ZW5hbnQuaWQpO1xuICAgICAgXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgdXNlciBwcm9maWxlJyxcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENyZWF0ZSB1c2VyLXRlbmFudCBhc3NvY2lhdGlvblxuICAgIGNvbnN0IHsgZXJyb3I6IGFzc29jaWF0aW9uRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl90ZW5hbnRzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICB1c2VyX2lkOiBhdXRoRGF0YS51c2VyLmlkLFxuICAgICAgICB0ZW5hbnRfaWQ6IHRlbmFudC5pZCxcbiAgICAgICAgcm9sZTogJ293bmVyJyxcbiAgICAgICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgICAgfSk7XG5cbiAgICBpZiAoYXNzb2NpYXRpb25FcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVXNlci10ZW5hbnQgYXNzb2NpYXRpb24gZXJyb3I6JywgYXNzb2NpYXRpb25FcnJvcik7XG4gICAgICBcbiAgICAgIC8vIENsZWFuIHVwIGNyZWF0ZWQgcmVjb3Jkc1xuICAgICAgYXdhaXQgc3VwYWJhc2UuYXV0aC5hZG1pbi5kZWxldGVVc2VyKGF1dGhEYXRhLnVzZXIuaWQpO1xuICAgICAgYXdhaXQgc3VwYWJhc2UuZnJvbSgndGVuYW50cycpLmRlbGV0ZSgpLmVxKCdpZCcsIHRlbmFudC5pZCk7XG4gICAgICBhd2FpdCBzdXBhYmFzZS5mcm9tKCd1c2VycycpLmRlbGV0ZSgpLmVxKCdpZCcsIHVzZXIuaWQpO1xuICAgICAgXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgYWNjb3VudCBhc3NvY2lhdGlvbicsXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBzZXNzaW9uIHRva2Vuc1xuICAgIGNvbnN0IHsgZGF0YTogc2Vzc2lvbkRhdGEsIGVycm9yOiBzZXNzaW9uRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguYWRtaW4uZ2VuZXJhdGVMaW5rKHtcbiAgICAgIHR5cGU6ICdzaWdudXAnLFxuICAgICAgZW1haWw6IGFkbWluRW1haWwsXG4gICAgfSk7XG5cbiAgICBpZiAoc2Vzc2lvbkVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTZXNzaW9uIGdlbmVyYXRpb24gZXJyb3I6Jywgc2Vzc2lvbkVycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGdlbmVyYXRlIHNlc3Npb24nLFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIGEgcHJvcGVyIHNlc3Npb25cbiAgICBjb25zdCB7IGRhdGE6IHNpZ25JbkRhdGEsIGVycm9yOiBzaWduSW5FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgZW1haWw6IGFkbWluRW1haWwsXG4gICAgICBwYXNzd29yZDogcGFzc3dvcmQsXG4gICAgfSk7XG5cbiAgICBsZXQgc2Vzc2lvblRva2VucyA9IG51bGw7XG4gICAgaWYgKHNpZ25JbkRhdGEuc2Vzc2lvbikge1xuICAgICAgc2Vzc2lvblRva2VucyA9IHtcbiAgICAgICAgYWNjZXNzX3Rva2VuOiBzaWduSW5EYXRhLnNlc3Npb24uYWNjZXNzX3Rva2VuLFxuICAgICAgICByZWZyZXNoX3Rva2VuOiBzaWduSW5EYXRhLnNlc3Npb24ucmVmcmVzaF90b2tlbixcbiAgICAgICAgZXhwaXJlc19pbjogc2lnbkluRGF0YS5zZXNzaW9uLmV4cGlyZXNfaW4gfHwgMzYwMCxcbiAgICAgIH07XG4gICAgfVxuXG4gICAgLy8gUmV0dXJuIHN1Y2Nlc3MgcmVzcG9uc2VcbiAgICBjb25zdCByZXNwb25zZSA9IHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnQWNjb3VudCBjcmVhdGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHRlbmFudDoge1xuICAgICAgICAgIGlkOiB0ZW5hbnQuaWQsXG4gICAgICAgICAgbmFtZTogdGVuYW50Lm5hbWUsXG4gICAgICAgICAgc2x1ZzogdGVuYW50LnNsdWcsXG4gICAgICAgIH0sXG4gICAgICAgIHVzZXI6IHtcbiAgICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgfSxcbiAgICAgICAgc2Vzc2lvbjogc2Vzc2lvblRva2VucyxcbiAgICAgIH0sXG4gICAgfTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXNwb25zZSwgeyBzdGF0dXM6IDIwMSB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1NpZ251cCBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLFxuICAgICAgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn0iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2aWNlQ2xpZW50Iiwic2lnbnVwRm9ybVNjaGVtYSIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJ2YWxpZGF0aW9uIiwic2FmZVBhcnNlIiwic3VjY2VzcyIsImZpZWxkRXJyb3JzIiwiZXJyb3IiLCJlcnJvcnMiLCJmb3JFYWNoIiwiZmllbGQiLCJwYXRoIiwicHVzaCIsIm1lc3NhZ2UiLCJkZXRhaWxzIiwic3RhdHVzIiwiYnVzaW5lc3NOYW1lIiwidGVuYW50U2x1ZyIsImFkbWluRW1haWwiLCJwYXNzd29yZCIsImRhdGEiLCJzdXBhYmFzZSIsImV4aXN0aW5nVGVuYW50IiwiZnJvbSIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiZXhpc3RpbmdVc2VyIiwiYXV0aERhdGEiLCJhdXRoRXJyb3IiLCJhdXRoIiwiYWRtaW4iLCJjcmVhdGVVc2VyIiwiZW1haWwiLCJlbWFpbF9jb25maXJtIiwidXNlciIsImNvbnNvbGUiLCJ0ZW5hbnQiLCJ0ZW5hbnRFcnJvciIsImluc2VydCIsIm5hbWUiLCJzbHVnIiwiZGVsZXRlVXNlciIsImlkIiwidXNlckVycm9yIiwiZmlyc3RfbmFtZSIsImRlbGV0ZSIsImFzc29jaWF0aW9uRXJyb3IiLCJ1c2VyX2lkIiwidGVuYW50X2lkIiwicm9sZSIsImlzX2FjdGl2ZSIsInNlc3Npb25EYXRhIiwic2Vzc2lvbkVycm9yIiwiZ2VuZXJhdGVMaW5rIiwidHlwZSIsInNpZ25JbkRhdGEiLCJzaWduSW5FcnJvciIsInNpZ25JbldpdGhQYXNzd29yZCIsInNlc3Npb25Ub2tlbnMiLCJzZXNzaW9uIiwiYWNjZXNzX3Rva2VuIiwicmVmcmVzaF90b2tlbiIsImV4cGlyZXNfaW4iLCJyZXNwb25zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/signup/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createSupabaseServerClient: () => (/* binding */ createSupabaseServerClient),\n/* harmony export */   createSupabaseServiceClient: () => (/* binding */ createSupabaseServiceClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Server-side Supabase client for Next.js with multi-tenant support\n */ \n\nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\nconst createClient = (cookieStore)=>{\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>{\n                        cookieStore.set(name, value, options);\n                    });\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Legacy function for backward compatibility\nasync function createSupabaseServerClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return createClient(cookieStore);\n}\n// Service role client for admin operations\nfunction createSupabaseServiceClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://cgzcndxnfldupgdddnra.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // Service role client doesn't need cookies\n            }\n        },\n        auth: {\n            persistSession: false,\n            autoRefreshToken: false,\n            detectSessionInUrl: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validations/signup.ts":
/*!***************************************!*\
  !*** ./src/lib/validations/signup.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   businessNameSchema: () => (/* binding */ businessNameSchema),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   passwordSchema: () => (/* binding */ passwordSchema),\n/* harmony export */   signupErrorSchema: () => (/* binding */ signupErrorSchema),\n/* harmony export */   signupFormSchema: () => (/* binding */ signupFormSchema),\n/* harmony export */   signupResponseSchema: () => (/* binding */ signupResponseSchema),\n/* harmony export */   tenantSlugSchema: () => (/* binding */ tenantSlugSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/**\n * Zod validation schemas for tenant admin signup\n */ \n// Business name validation\nconst businessNameSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, \"Business name must be at least 2 characters\").max(100, \"Business name cannot exceed 100 characters\").regex(/^[a-zA-Z0-9\\s\\-'&.]+$/, \"Business name contains invalid characters\").transform((val)=>val.trim());\n// Tenant slug validation\nconst tenantSlugSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(3, \"Tenant slug must be at least 3 characters\").max(50, \"Tenant slug cannot exceed 50 characters\").regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, \"Slug can only contain lowercase letters, numbers, and hyphens\").refine((val)=>!val.startsWith(\"-\") && !val.endsWith(\"-\"), \"Slug cannot start or end with hyphen\").refine((val)=>!val.includes(\"--\"), \"Slug cannot contain consecutive hyphens\").transform((val)=>val.toLowerCase().trim());\n// Email validation\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Please enter a valid email address\").max(255, \"Email cannot exceed 255 characters\").transform((val)=>val.toLowerCase().trim());\n// Password validation\nconst passwordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Password must be at least 8 characters\").max(128, \"Password cannot exceed 128 characters\").regex(/[A-Z]/, \"Password must contain at least one uppercase letter\").regex(/[a-z]/, \"Password must contain at least one lowercase letter\").regex(/\\d/, \"Password must contain at least one number\").regex(/[!@#$%^&*(),.?\":{}|<>]/, \"Password must contain at least one special character\");\n// Complete signup form schema\nconst signupFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    businessName: businessNameSchema,\n    tenantSlug: tenantSlugSchema,\n    adminEmail: emailSchema,\n    password: passwordSchema\n});\n// API response schemas\nconst signupResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    success: zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        tenant: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            slug: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n        }),\n        user: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            email: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n        }),\n        session: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            access_token: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            refresh_token: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            expires_in: zod__WEBPACK_IMPORTED_MODULE_0__.number()\n        })\n    }).optional()\n});\nconst signupErrorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    success: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    details: zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())).optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validations/signup.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();