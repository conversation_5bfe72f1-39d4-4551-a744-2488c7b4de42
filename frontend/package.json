{"name": "tapdine-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^6.12.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.38.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "next": "14.0.0", "postcss": "^8.4.31", "prisma": "^6.12.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.47.0", "tailwindcss": "^3.3.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.8.7", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "typescript": "^5.2.2"}}